import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { RouterModule, Router } from '@angular/router';
import { NzLayoutModule } from 'ng-zorro-antd/layout';
import { NzMenuModule } from 'ng-zorro-antd/menu';
import { NzIconModule } from 'ng-zorro-antd/icon';
import { NzButtonModule } from 'ng-zorro-antd/button';

import { NzAvatarModule } from 'ng-zorro-antd/avatar';
import { NzMessageService } from 'ng-zorro-antd/message';

import { CustomerAuthService } from '../../../core/services/customer-auth.service';
import { User } from '../../../core/models';

@Component({
  selector: 'app-customer-layout',
  standalone: true,
  imports: [
    CommonModule,
    RouterModule,
    NzLayoutModule,
    NzMenuModule,
    NzIconModule,
    NzButtonModule,

    NzAvatarModule
  ],
  templateUrl: './customer-layout.component.html',
  styleUrls: ['./customer-layout.component.scss']
})
export class CustomerLayoutComponent implements OnInit {
  currentUser: User | null = null;
  isCollapsed = false;

  constructor(
    private customerAuthService: CustomerAuthService,
    private router: Router,
    private message: NzMessageService
  ) {}

  ngOnInit(): void {
    this.customerAuthService.currentCustomer$.subscribe(user => {
      this.currentUser = user;
    });
  }

  toggleCollapsed(): void {
    this.isCollapsed = !this.isCollapsed;
  }

  onLogout(): void {
    this.customerAuthService.logout().subscribe({
      next: () => {
        this.message.success('Logged out successfully');
        this.router.navigate(['/']);
      },
      error: () => {
        // Even if logout fails on server, clear local data
        this.router.navigate(['/']);
      }
    });
  }

  navigateToProfile(): void {
    this.router.navigate(['/customer/profile']);
  }

  getInitials(name: string): string {
    return name
      .split(' ')
      .map(word => word.charAt(0))
      .join('')
      .toUpperCase()
      .substring(0, 2);
  }
}
