import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { RouterModule, ActivatedRoute, Router } from '@angular/router';
import { ReactiveFormsModule, FormBuilder, FormGroup, Validators } from '@angular/forms';
import { NzFormModule } from 'ng-zorro-antd/form';
import { NzInputModule } from 'ng-zorro-antd/input';
import { NzButtonModule } from 'ng-zorro-antd/button';
import { NzCardModule } from 'ng-zorro-antd/card';
import { NzDatePickerModule } from 'ng-zorro-antd/date-picker';
import { NzMessageService } from 'ng-zorro-antd/message';
import { PageHeaderComponent } from '../../../shared/components/page-header/page-header.component';
import { CustomerService } from '../../../core/services';
import { Customer, CreateCustomerRequest, UpdateCustomerRequest } from '../../../core/models';
import { CustomValidators } from '../../../shared/validators/custom.validators';

@Component({
  selector: 'app-customer-form',
  standalone: true,
  imports: [
    CommonModule,
    RouterModule,
    ReactiveFormsModule,
    NzFormModule,
    NzInputModule,
    NzButtonModule,
    NzCardModule,
    NzDatePickerModule,
    PageHeaderComponent
  ],
  templateUrl: './customer-form.component.html',
  styleUrls: ['./customer-form.component.scss']
})
export class CustomerFormComponent implements OnInit {
  customerForm!: FormGroup;
  isEditMode = false;
  customerId: number | null = null;
  loading = false;
  submitting = false;

  constructor(
    private fb: FormBuilder,
    private route: ActivatedRoute,
    private router: Router,
    private customerService: CustomerService,
    private message: NzMessageService
  ) {}

  ngOnInit(): void {
    this.initForm();
    this.checkEditMode();
  }

  private initForm(): void {
    this.customerForm = this.fb.group({
      fullName: ['', [Validators.required, Validators.minLength(2)]],
      email: ['', [Validators.required, Validators.email]],
      phone: ['', [Validators.required, CustomValidators.phoneNumber]],
      address: ['', [Validators.required]],
      idCardNumber: ['', [Validators.required, CustomValidators.idCardNumber]],
      driverLicenseNumber: ['', [CustomValidators.driverLicenseNumber]],
      licenseIssueDate: [null],
      licenseExpiryDate: [null]
    });

    // Add conditional validation for license dates
    this.customerForm.get('driverLicenseNumber')?.valueChanges.subscribe(value => {
      const issueControl = this.customerForm.get('licenseIssueDate');
      const expiryControl = this.customerForm.get('licenseExpiryDate');
      
      if (value) {
        issueControl?.setValidators([Validators.required, CustomValidators.pastDate]);
        expiryControl?.setValidators([Validators.required, CustomValidators.futureDate]);
      } else {
        issueControl?.clearValidators();
        expiryControl?.clearValidators();
        issueControl?.setValue(null);
        expiryControl?.setValue(null);
      }
      
      issueControl?.updateValueAndValidity();
      expiryControl?.updateValueAndValidity();
    });
  }

  private checkEditMode(): void {
    const id = this.route.snapshot.paramMap.get('id');
    if (id) {
      this.isEditMode = true;
      this.customerId = +id;
      this.loadCustomer();
    }
  }

  private loadCustomer(): void {
    if (this.customerId) {
      this.loading = true;
      this.customerService.getCustomer(this.customerId).subscribe({
        next: (customer) => {
          this.customerForm.patchValue({
            ...customer,
            licenseIssueDate: customer.licenseIssueDate ? new Date(customer.licenseIssueDate) : null,
            licenseExpiryDate: customer.licenseExpiryDate ? new Date(customer.licenseExpiryDate) : null
          });
          this.loading = false;
        },
        error: (error) => {
          this.message.error('Failed to load customer');
          this.loading = false;
        }
      });
    }
  }

  onSubmit(): void {
    if (this.customerForm.valid) {
      this.submitting = true;
      
      const formValue = this.customerForm.value;
      const customerData = {
        ...formValue,
        licenseIssueDate: formValue.licenseIssueDate ? new Date(formValue.licenseIssueDate) : undefined,
        licenseExpiryDate: formValue.licenseExpiryDate ? new Date(formValue.licenseExpiryDate) : undefined
      };
      
      if (this.isEditMode && this.customerId) {
        const updateRequest: UpdateCustomerRequest = {
          id: this.customerId,
          ...customerData
        };
        
        this.customerService.updateCustomer(updateRequest).subscribe({
          next: () => {
            this.message.success('Customer updated successfully');
            this.router.navigate(['/customers']);
          },
          error: (error) => {
            this.message.error('Failed to update customer');
            this.submitting = false;
          }
        });
      } else {
        const createRequest: CreateCustomerRequest = customerData;
        
        this.customerService.createCustomer(createRequest).subscribe({
          next: () => {
            this.message.success('Customer created successfully');
            this.router.navigate(['/customers']);
          },
          error: (error) => {
            this.message.error('Failed to create customer');
            this.submitting = false;
          }
        });
      }
    } else {
      this.markFormGroupTouched();
    }
  }

  private markFormGroupTouched(): void {
    Object.keys(this.customerForm.controls).forEach(key => {
      const control = this.customerForm.get(key);
      if (control) {
        control.markAsTouched();
      }
    });
  }

  onCancel(): void {
    this.router.navigate(['/customers']);
  }

  disabledDate = (current: Date): boolean => {
    const today = new Date();
    today.setHours(0, 0, 0, 0);
    return current && current > today;
  };

  disabledExpiryDate = (current: Date): boolean => {
    const today = new Date();
    today.setHours(0, 0, 0, 0);
    return current && current <= today;
  };
}
