import { Routes } from '@angular/router';

export const vehicleRoutes: Routes = [
  {
    path: '',
    redirectTo: 'list',
    pathMatch: 'full'
  },
  {
    path: 'list',
    loadComponent: () => import('./vehicle-list/vehicle-list.component').then(m => m.VehicleListComponent)
  },
  {
    path: 'create',
    loadComponent: () => import('./vehicle-form/vehicle-form.component').then(m => m.VehicleFormComponent)
  },
  {
    path: 'edit/:id',
    loadComponent: () => import('./vehicle-form/vehicle-form.component').then(m => m.VehicleFormComponent)
  },
  {
    path: 'types',
    loadComponent: () => import('./vehicle-type-list/vehicle-type-list.component').then(m => m.VehicleTypeListComponent)
  }
];
