import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { RouterModule, ActivatedRoute, Router } from '@angular/router';
import { ReactiveFormsModule, FormBuilder, FormGroup, Validators } from '@angular/forms';
import { NzFormModule } from 'ng-zorro-antd/form';
import { NzInputModule } from 'ng-zorro-antd/input';
import { NzInputNumberModule } from 'ng-zorro-antd/input-number';
import { NzButtonModule } from 'ng-zorro-antd/button';
import { NzCardModule } from 'ng-zorro-antd/card';
import { NzSelectModule } from 'ng-zorro-antd/select';
import { NzDatePickerModule } from 'ng-zorro-antd/date-picker';
import { NzMessageService } from 'ng-zorro-antd/message';
import { PageHeaderComponent } from '../../../shared/components/page-header/page-header.component';
import { PaymentService, ContractService } from '../../../core/services';
import { 
  Payment, 
  CreatePaymentRequest, 
  UpdatePaymentRequest, 
  PaymentStatus, 
  PaymentMethod,
  Contract
} from '../../../core/models';
import { CustomValidators } from '../../../shared/validators/custom.validators';

@Component({
  selector: 'app-payment-form',
  standalone: true,
  imports: [
    CommonModule,
    RouterModule,
    ReactiveFormsModule,
    NzFormModule,
    NzInputModule,
    NzInputNumberModule,
    NzButtonModule,
    NzCardModule,
    NzSelectModule,
    NzDatePickerModule,
    PageHeaderComponent
  ],
  templateUrl: './payment-form.component.html',
  styleUrls: ['./payment-form.component.scss']
})
export class PaymentFormComponent implements OnInit {
  paymentForm!: FormGroup;
  isEditMode = false;
  paymentId: number | null = null;
  loading = false;
  submitting = false;

  contracts: Contract[] = [];
  paymentStatusOptions = Object.values(PaymentStatus);
  paymentMethodOptions = Object.values(PaymentMethod);

  constructor(
    private fb: FormBuilder,
    private route: ActivatedRoute,
    private router: Router,
    private paymentService: PaymentService,
    private contractService: ContractService,
    private message: NzMessageService
  ) {}

  ngOnInit(): void {
    this.initForm();
    this.loadContracts();
    this.checkEditMode();
  }

  private initForm(): void {
    this.paymentForm = this.fb.group({
      contractId: [null, [Validators.required]],
      amount: [null, [Validators.required, CustomValidators.positiveNumber]],
      paymentMethod: [PaymentMethod.CASH, [Validators.required]],
      status: [PaymentStatus.PENDING],
      note: ['']
    });
  }

  private loadContracts(): void {
    this.contractService.getContracts({ limit: 1000 }).subscribe({
      next: (response) => {
        this.contracts = response.data;
      },
      error: () => {
        this.message.error('Failed to load contracts');
      }
    });
  }

  private checkEditMode(): void {
    const id = this.route.snapshot.paramMap.get('id');
    if (id) {
      this.isEditMode = true;
      this.paymentId = +id;
      this.loadPayment();
    }
  }

  private loadPayment(): void {
    if (this.paymentId) {
      this.loading = true;
      this.paymentService.getPayment(this.paymentId).subscribe({
        next: (payment) => {
          this.paymentForm.patchValue({
            contractId: payment.contractId,
            amount: payment.amount,
            paymentMethod: payment.paymentMethod,
            status: payment.status,
            note: payment.note
          });
          this.loading = false;
        },
        error: (error) => {
          this.message.error('Failed to load payment');
          this.loading = false;
        }
      });
    }
  }

  onSubmit(): void {
    if (this.paymentForm.valid) {
      this.submitting = true;
      
      const formValue = this.paymentForm.value;
      
      if (this.isEditMode && this.paymentId) {
        const updateRequest: UpdatePaymentRequest = {
          id: this.paymentId,
          ...formValue
        };
        
        this.paymentService.updatePayment(updateRequest).subscribe({
          next: () => {
            this.message.success('Payment updated successfully');
            this.router.navigate(['/payments']);
          },
          error: (error) => {
            this.message.error('Failed to update payment');
            this.submitting = false;
          }
        });
      } else {
        const createRequest: CreatePaymentRequest = formValue;
        
        this.paymentService.createPayment(createRequest).subscribe({
          next: () => {
            this.message.success('Payment created successfully');
            this.router.navigate(['/payments']);
          },
          error: (error) => {
            this.message.error('Failed to create payment');
            this.submitting = false;
          }
        });
      }
    } else {
      this.markFormGroupTouched();
    }
  }

  private markFormGroupTouched(): void {
    Object.keys(this.paymentForm.controls).forEach(key => {
      const control = this.paymentForm.get(key);
      if (control) {
        control.markAsTouched();
      }
    });
  }

  onCancel(): void {
    this.router.navigate(['/payments']);
  }

  onContractChange(contractId: number): void {
    const selectedContract = this.contracts.find(c => c.id === contractId);
    if (selectedContract) {
      // Auto-fill amount with remaining balance or total price
      this.paymentForm.get('amount')?.setValue(selectedContract.totalPrice);
    }
  }

  getPaymentMethodIcon(method: PaymentMethod): string {
    const iconMap: { [key in PaymentMethod]: string } = {
      [PaymentMethod.CASH]: 'dollar',
      [PaymentMethod.CREDIT_CARD]: 'credit-card',
      [PaymentMethod.DEBIT_CARD]: 'credit-card',
      [PaymentMethod.BANK_TRANSFER]: 'bank',
      [PaymentMethod.DIGITAL_WALLET]: 'mobile'
    };
    return iconMap[method] || 'dollar';
  }
}
