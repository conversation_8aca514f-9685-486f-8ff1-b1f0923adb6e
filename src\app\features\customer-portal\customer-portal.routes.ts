import { Routes } from '@angular/router';
import { CustomerAuthGuard } from '../../core/guards/customer-auth.guard';

export const customerPortalRoutes: Routes = [
  // Public routes (no authentication required)
  {
    path: 'login',
    loadComponent: () => import('./auth/login/login.component').then(m => m.CustomerLoginComponent)
  },
  {
    path: 'register',
    loadComponent: () => import('./auth/register/register.component').then(m => m.CustomerRegisterComponent)
  },
  {
    path: 'vehicles',
    loadComponent: () => import('./vehicles/vehicle-browse/vehicle-browse.component').then(m => m.VehicleBrowseComponent)
  },
  {
    path: 'vehicles/:id',
    loadComponent: () => import('./vehicles/vehicle-details/vehicle-details.component').then(m => m.VehicleDetailsComponent)
  },
  
  // Protected routes (authentication required)
  {
    path: '',
    loadComponent: () => import('./layout/customer-layout.component').then(m => m.CustomerLayoutComponent),
    // canActivate: [CustomerAuthGuard],
    children: [
      {
        path: 'dashboard',
        loadComponent: () => import('./dashboard/dashboard.component').then(m => m.CustomerDashboardComponent)
      },
      {
        path: 'profile',
        loadComponent: () => import('./profile/profile.component').then(m => m.CustomerProfileComponent)
      },
      {
        path: 'bookings',
        loadComponent: () => import('./bookings/bookings.component').then(m => m.CustomerBookingsComponent)
      },
      {
        path: 'booking/confirm',
        loadComponent: () => import('./booking/booking-confirm/booking-confirm.component').then(m => m.BookingConfirmComponent)
      },
      {
        path: '',
        redirectTo: 'dashboard',
        pathMatch: 'full'
      }
    ]
  }
];
