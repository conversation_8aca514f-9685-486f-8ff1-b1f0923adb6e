.booking-confirm-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 20px;
  background: #f5f5f5;
  min-height: 100vh;
}

.page-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 24px;
  background: white;
  padding: 20px;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  
  .back-button {
    color: #1890ff;
    padding: 0;
    
    &:hover {
      color: #40a9ff;
    }
  }
  
  h1 {
    margin: 0;
    font-size: 28px;
    font-weight: 600;
    color: #333;
  }
}

.steps-card {
  margin-bottom: 24px;
  border-radius: 12px;
  
  ::ng-deep .ant-card-body {
    padding: 24px;
  }
}

.details-card,
.payment-card,
.confirmation-card {
  border-radius: 12px;
  
  ::ng-deep .ant-card-head {
    border-bottom: 1px solid #f0f0f0;
    
    .ant-card-head-title {
      font-size: 20px;
      font-weight: 600;
    }
  }
}

.vehicle-info {
  display: flex;
  align-items: center;
  gap: 16px;
  
  .vehicle-thumbnail {
    width: 80px;
    height: 60px;
    object-fit: cover;
    border-radius: 8px;
  }
  
  .vehicle-details {
    .vehicle-name {
      font-size: 16px;
      font-weight: 600;
      color: #333;
    }
    
    .vehicle-plate {
      font-size: 14px;
      color: #1890ff;
      margin: 4px 0;
    }
    
    .vehicle-specs {
      font-size: 12px;
      color: #666;
    }
  }
}

.duration {
  color: #666;
  font-size: 14px;
  margin-left: 8px;
}

.confirmation-content {
  .confirmation-message {
    text-align: center;
    margin-bottom: 32px;
    
    .success-icon {
      font-size: 48px;
      color: #52c41a;
      margin-bottom: 16px;
    }
    
    h3 {
      font-size: 24px;
      font-weight: 600;
      color: #333;
      margin-bottom: 8px;
    }
    
    p {
      color: #666;
      font-size: 16px;
    }
  }
}

.step-actions {
  margin-top: 32px;
  text-align: right;
  padding-top: 24px;
  border-top: 1px solid #f0f0f0;
}

.summary-panel {
  position: sticky;
  top: 20px;
}

.summary-card {
  border-radius: 12px;
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
  
  ::ng-deep .ant-card-head {
    border-bottom: 1px solid #f0f0f0;
    
    .ant-card-head-title {
      font-size: 20px;
      font-weight: 600;
      color: #1890ff;
    }
  }
}

.summary-section {
  h4 {
    font-size: 16px;
    font-weight: 600;
    color: #333;
    margin-bottom: 12px;
  }
  
  .summary-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 8px;
    
    &:last-child {
      margin-bottom: 0;
    }
    
    &.total {
      font-size: 18px;
      font-weight: 600;
      color: #1890ff;
      padding-top: 8px;
      border-top: 1px solid #f0f0f0;
    }
    
    &.deposit {
      font-weight: 500;
      color: #fa8c16;
    }
  }
}

.notes-list {
  margin: 0;
  padding-left: 16px;
  
  li {
    font-size: 14px;
    color: #666;
    margin-bottom: 8px;
    
    &:last-child {
      margin-bottom: 0;
    }
  }
}

// Responsive design
@media (max-width: 768px) {
  .booking-confirm-container {
    padding: 10px;
  }
  
  .page-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 12px;
    
    h1 {
      font-size: 24px;
    }
  }
  
  .vehicle-info {
    flex-direction: column;
    align-items: flex-start;
    text-align: left;
    
    .vehicle-thumbnail {
      width: 100%;
      height: 120px;
    }
  }
  
  .summary-panel {
    position: static;
    margin-top: 24px;
  }
  
  .step-actions {
    text-align: center;
    
    button {
      width: 100%;
      margin: 4px 0 !important;
    }
  }
}

@media (max-width: 576px) {
  .confirmation-message {
    .success-icon {
      font-size: 36px;
    }
    
    h3 {
      font-size: 20px;
    }
    
    p {
      font-size: 14px;
    }
  }
}
