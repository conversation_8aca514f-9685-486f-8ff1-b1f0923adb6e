import { Injectable } from '@angular/core';
import { HttpInterceptor, HttpRequest, HttpHandler, HttpEvent } from '@angular/common/http';
import { Observable } from 'rxjs';
import { finalize } from 'rxjs/operators';
import { LoadingService } from '../services/loading.service';

@Injectable()
export class LoadingInterceptor implements HttpInterceptor {
  constructor(private loadingService: LoadingService) {}

  intercept(request: HttpRequest<any>, next: <PERSON>ttpHandler): Observable<HttpEvent<any>> {
    // Show loading spinner
    this.loadingService.show();

    return next.handle(request).pipe(
      finalize(() => {
        // Hide loading spinner when request completes (success or error)
        this.loadingService.hide();
      })
    );
  }
}
