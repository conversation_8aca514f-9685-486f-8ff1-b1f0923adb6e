<nz-layout class="app-layout">
  <nz-sider 
    class="menu-sidebar"
    nzCollapsible
    [(nzCollapsed)]="isCollapsed"
    [nzTrigger]="null">
    
    <div class="sidebar-logo">
      <h3 *ngIf="!isCollapsed">Car Rental</h3>
      <h3 *ngIf="isCollapsed">CR</h3>
    </div>
    
    <ul nz-menu nzTheme="dark" nzMode="inline" [nzInlineCollapsed]="isCollapsed">
      <li nz-menu-item nzMatchRouter>
        <a routerLink="/dashboard">
          <span nz-icon nzType="dashboard"></span>
          <span>Dashboard</span>
        </a>
      </li>
      
      <li nz-submenu nzTitle="Vehicle Management" nzIcon="car">
        <ul>
          <li nz-menu-item nzMatchRouter>
            <a routerLink="/vehicles/types">Vehicle Types</a>
          </li>
          <li nz-menu-item nzMatchRouter>
            <a routerLink="/vehicles">Vehicles</a>
          </li>
        </ul>
      </li>
      
      <li nz-menu-item nzMatchRouter>
        <a routerLink="/customers">
          <span nz-icon nzType="user"></span>
          <span>Customers</span>
        </a>
      </li>
      
      <li nz-menu-item nzMatchRouter>
        <a routerLink="/drivers">
          <span nz-icon nzType="team"></span>
          <span>Drivers</span>
        </a>
      </li>
      
      <li nz-menu-item nzMatchRouter>
        <a routerLink="/contracts">
          <span nz-icon nzType="file-text"></span>
          <span>Contracts</span>
        </a>
      </li>
      
      <li nz-menu-item nzMatchRouter>
        <a routerLink="/payments">
          <span nz-icon nzType="dollar"></span>
          <span>Payments</span>
        </a>
      </li>
      
      <li nz-menu-item nzMatchRouter>
        <a routerLink="/maintenance">
          <span nz-icon nzType="tool"></span>
          <span>Maintenance</span>
        </a>
      </li>
    </ul>
  </nz-sider>
  
  <nz-layout>
    <nz-header class="app-header">
      <span
        class="trigger"
        nz-icon
        [nzType]="isCollapsed ? 'menu-unfold' : 'menu-fold'"
        (click)="isCollapsed = !isCollapsed">
      </span>
      
      <div class="header-right">
        <div class="user-dropdown" nz-dropdown [nzDropdownMenu]="userMenu" nzTrigger="click" nzPlacement="bottomRight">
          <nz-avatar nzIcon="user"></nz-avatar>
          <span class="user-name" *ngIf="currentUser$ | async as user">
            {{ user.fullName }}
          </span>
          <span nz-icon nzType="down"></span>
        </div>
        <nz-dropdown-menu #userMenu="nzDropdownMenu">
          <ul nz-menu nzSelectable="false">
            <li nz-menu-item>
              <span nz-icon nzType="user"></span>
              Profile
            </li>
            <li nz-menu-item>
              <span nz-icon nzType="setting"></span>
              Settings
            </li>
            <li nz-menu-divider></li>
            <li nz-menu-item (click)="logout()">
              <span nz-icon nzType="logout"></span>
              Logout
            </li>
          </ul>
        </nz-dropdown-menu>
      </div>
    </nz-header>
    
    <nz-content class="app-content">
      <router-outlet></router-outlet>
    </nz-content>
  </nz-layout>
</nz-layout>

<app-loading-spinner></app-loading-spinner>
