.bookings-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 20px;
  background: #f5f5f5;
  min-height: 100vh;
}

.page-header {
  text-align: center;
  margin-bottom: 32px;
  background: white;
  padding: 40px;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  
  h1 {
    font-size: 32px;
    font-weight: 600;
    color: #333;
    margin: 0 0 8px 0;
  }
  
  p {
    font-size: 16px;
    color: #666;
    margin: 0;
  }
}

.filter-card {
  margin-bottom: 24px;
  border-radius: 12px;
  
  .filter-form {
    .filter-actions {
      text-align: right;
      margin-top: 16px;
    }
  }
}

.bookings-card {
  border-radius: 12px;
  
  ::ng-deep .ant-card-body {
    padding: 0;
  }
}

nz-table {
  ::ng-deep .ant-table-thead > tr > th {
    background-color: #fafafa;
    font-weight: 600;
  }
  
  ::ng-deep .ant-table-tbody > tr:hover > td {
    background-color: #f5f5f5;
  }
}

.contract-info {
  .contract-number {
    font-weight: 600;
    color: #333;
  }
  
  .contract-date {
    font-size: 12px;
    color: #666;
    margin-top: 2px;
  }
}

.vehicle-info {
  .vehicle-name {
    font-weight: 500;
    color: #333;
  }
  
  .vehicle-plate {
    font-size: 12px;
    color: #1890ff;
    margin-top: 2px;
  }
}

.rental-period {
  .duration {
    font-size: 12px;
    color: #666;
    margin-top: 2px;
  }
}

.rental-type {
  font-size: 14px;
  color: #666;
}

.amount {
  font-weight: 600;
  color: #1890ff;
  font-size: 16px;
}

.actions {
  display: flex;
  flex-wrap: wrap;
  gap: 4px;
  
  button {
    margin: 0;
  }
}

.empty-state {
  padding: 60px 0;
  text-align: center;
}

nz-tag {
  margin: 0;
  
  span {
    margin-right: 4px;
  }
}

// Responsive design
@media (max-width: 768px) {
  .bookings-container {
    padding: 10px;
  }
  
  .page-header {
    padding: 30px 20px;
    
    h1 {
      font-size: 24px;
    }
    
    p {
      font-size: 14px;
    }
  }
  
  .filter-card {
    ::ng-deep .ant-card-body {
      padding: 16px;
    }
  }
  
  .filter-form .filter-actions {
    text-align: center;
    margin-top: 20px;
    
    button {
      width: 100%;
    }
  }
  
  // Hide some columns on mobile
  ::ng-deep .ant-table {
    .ant-table-thead > tr > th:nth-child(4),
    .ant-table-tbody > tr > td:nth-child(4) {
      display: none;
    }
  }
  
  .actions {
    flex-direction: column;
    
    button {
      width: 100%;
      margin-bottom: 4px;
    }
  }
}

@media (max-width: 576px) {
  .page-header {
    padding: 20px 15px;
    
    h1 {
      font-size: 20px;
    }
  }
  
  // Hide more columns on small mobile
  ::ng-deep .ant-table {
    .ant-table-thead > tr > th:nth-child(3),
    .ant-table-tbody > tr > td:nth-child(3),
    .ant-table-thead > tr > th:nth-child(4),
    .ant-table-tbody > tr > td:nth-child(4) {
      display: none;
    }
  }
  
  .contract-info,
  .vehicle-info {
    .contract-number,
    .vehicle-name {
      font-size: 14px;
    }
    
    .contract-date,
    .vehicle-plate {
      font-size: 11px;
    }
  }
}
