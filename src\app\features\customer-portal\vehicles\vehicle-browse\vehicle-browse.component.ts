import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { RouterModule } from '@angular/router';
import { ReactiveFormsModule, FormBuilder, FormGroup } from '@angular/forms';
import { NzCardModule } from 'ng-zorro-antd/card';
import { NzButtonModule } from 'ng-zorro-antd/button';
import { NzInputModule } from 'ng-zorro-antd/input';
import { NzSelectModule } from 'ng-zorro-antd/select';
import { NzFormModule } from 'ng-zorro-antd/form';
import { NzIconModule } from 'ng-zorro-antd/icon';
import { NzTagModule } from 'ng-zorro-antd/tag';
import { NzDatePickerModule } from 'ng-zorro-antd/date-picker';
import { NzInputNumberModule } from 'ng-zorro-antd/input-number';
import { NzPaginationModule } from 'ng-zorro-antd/pagination';
import { NzEmptyModule } from 'ng-zorro-antd/empty';
import { NzSpinModule } from 'ng-zorro-antd/spin';
import { NzMessageService } from 'ng-zorro-antd/message';
import { Observable, BehaviorSubject } from 'rxjs';
import { debounceTime, distinctUntilChanged, switchMap } from 'rxjs/operators';

import { VehicleService, VehicleTypeService } from '../../../../core/services';
import { Vehicle, VehicleType, VehicleFilter, PaginatedResponse } from '../../../../core/models';
import { CurrencyFormatPipe } from '../../../../shared/pipes/currency-format.pipe';

@Component({
  selector: 'app-vehicle-browse',
  standalone: true,
  imports: [
    CommonModule,
    RouterModule,
    ReactiveFormsModule,
    NzCardModule,
    NzButtonModule,
    NzInputModule,
    NzSelectModule,
    NzFormModule,
    NzIconModule,
    NzTagModule,
    NzDatePickerModule,
    NzInputNumberModule,
    NzPaginationModule,
    NzEmptyModule,
    NzSpinModule,
    CurrencyFormatPipe
  ],
  templateUrl: './vehicle-browse.component.html',
  styleUrls: ['./vehicle-browse.component.scss']
})
export class VehicleBrowseComponent implements OnInit {
  vehicles: Vehicle[] = [];
  vehicleTypes: VehicleType[] = [];
  loading = false;
  total = 0;
  pageSize = 12;
  pageIndex = 1;
  
  searchForm!: FormGroup;
  private searchSubject = new BehaviorSubject<VehicleFilter>({});

  constructor(
    private fb: FormBuilder,
    private vehicleService: VehicleService,
    private vehicleTypeService: VehicleTypeService,
    private message: NzMessageService
  ) {}

  ngOnInit(): void {
    this.initSearchForm();
    this.loadVehicleTypes();
    this.setupSearchSubscription();
    this.loadVehicles();
  }

  private initSearchForm(): void {
    const today = new Date();
    const tomorrow = new Date(today);
    tomorrow.setDate(tomorrow.getDate() + 1);

    this.searchForm = this.fb.group({
      startDate: [today],
      endDate: [tomorrow],
      vehicleTypeId: [null],
      seatCount: [null],
      minPrice: [null],
      maxPrice: [null]
    });
  }

  private loadVehicleTypes(): void {
    this.vehicleTypeService.getAllVehicleTypes().subscribe({
      next: (types) => {
        this.vehicleTypes = types;
      },
      error: () => {
        this.message.error('Failed to load vehicle types');
      }
    });
  }

  private setupSearchSubscription(): void {
    this.searchForm.valueChanges.pipe(
      debounceTime(300),
      distinctUntilChanged()
    ).subscribe(values => {
      const filter: VehicleFilter = {
        ...values,
        status: 'AVAILABLE',
        page: 1,
        limit: this.pageSize
      };
      this.searchSubject.next(filter);
      this.pageIndex = 1;
    });

    this.searchSubject.pipe(
      switchMap(filter => {
        this.loading = true;
        // If dates are selected, get available vehicles for those dates
        if (filter.startDate && filter.endDate) {
          return this.vehicleService.getAvailableVehicles(filter.startDate, filter.endDate);
        } else {
          return this.vehicleService.getVehicles(filter);
        }
      })
    ).subscribe({
      next: (response: any) => {
        // Handle both array response (from getAvailableVehicles) and paginated response
        if (Array.isArray(response)) {
          this.vehicles = this.applyClientSideFilters(response);
          this.total = this.vehicles.length;
        } else {
          this.vehicles = response.data;
          this.total = response.total;
        }
        this.loading = false;
      },
      error: (error) => {
        this.message.error('Failed to load vehicles');
        this.loading = false;
      }
    });
  }

  private applyClientSideFilters(vehicles: Vehicle[]): Vehicle[] {
    const formValue = this.searchForm.value;
    let filtered = vehicles;

    if (formValue.vehicleTypeId) {
      filtered = filtered.filter(v => v.vehicleTypeId === formValue.vehicleTypeId);
    }

    if (formValue.seatCount) {
      filtered = filtered.filter(v => v.seatCount === formValue.seatCount);
    }

    if (formValue.minPrice) {
      filtered = filtered.filter(v => v.basePrice >= formValue.minPrice);
    }

    if (formValue.maxPrice) {
      filtered = filtered.filter(v => v.basePrice <= formValue.maxPrice);
    }

    return filtered;
  }

  private loadVehicles(): void {
    const filter: VehicleFilter = {
      ...this.searchForm.value,
      status: 'AVAILABLE',
      page: this.pageIndex,
      limit: this.pageSize
    };
    this.searchSubject.next(filter);
  }

  onPageChange(pageIndex: number): void {
    this.pageIndex = pageIndex;
    this.loadVehicles();
  }

  resetFilters(): void {
    const today = new Date();
    const tomorrow = new Date(today);
    tomorrow.setDate(tomorrow.getDate() + 1);
    
    this.searchForm.patchValue({
      startDate: today,
      endDate: tomorrow,
      vehicleTypeId: null,
      seatCount: null,
      minPrice: null,
      maxPrice: null
    });
  }

  viewVehicleDetails(vehicle: Vehicle): void {
    const startDate = this.searchForm.value.startDate;
    const endDate = this.searchForm.value.endDate;
    
    // Navigate with query params for dates
    const queryParams: any = {};
    if (startDate) queryParams.startDate = startDate.toISOString();
    if (endDate) queryParams.endDate = endDate.toISOString();
    
    // Navigate to vehicle details
    // This will be implemented when we create the routing
  }

  calculateRentalDays(): number {
    const startDate = this.searchForm.value.startDate;
    const endDate = this.searchForm.value.endDate;
    
    if (startDate && endDate) {
      const timeDiff = endDate.getTime() - startDate.getTime();
      return Math.ceil(timeDiff / (1000 * 3600 * 24));
    }
    return 1;
  }

  calculateTotalPrice(vehicle: Vehicle): number {
    return vehicle.basePrice * this.calculateRentalDays();
  }

  disabledDate = (current: Date): boolean => {
    const today = new Date();
    today.setHours(0, 0, 0, 0);
    return current && current < today;
  };

  disabledEndDate = (current: Date): boolean => {
    const startDate = this.searchForm.value.startDate;
    if (!startDate) return false;
    
    const start = new Date(startDate);
    start.setHours(0, 0, 0, 0);
    return current && current <= start;
  };
}
