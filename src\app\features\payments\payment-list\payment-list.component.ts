import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { RouterModule } from '@angular/router';
import { ReactiveFormsModule, FormBuilder, FormGroup } from '@angular/forms';
import { NzTableModule } from 'ng-zorro-antd/table';
import { NzButtonModule } from 'ng-zorro-antd/button';
import { NzInputModule } from 'ng-zorro-antd/input';
import { NzSelectModule } from 'ng-zorro-antd/select';
import { NzTagModule } from 'ng-zorro-antd/tag';
import { NzCardModule } from 'ng-zorro-antd/card';
import { NzFormModule } from 'ng-zorro-antd/form';
import { NzIconModule } from 'ng-zorro-antd/icon';
import { NzDividerModule } from 'ng-zorro-antd/divider';
import { NzDatePickerModule } from 'ng-zorro-antd/date-picker';
import { NzInputNumberModule } from 'ng-zorro-antd/input-number';
import { NzMessageService } from 'ng-zorro-antd/message';
import { Observable, BehaviorSubject } from 'rxjs';
import { debounceTime, distinctUntilChanged, switchMap } from 'rxjs/operators';

import { PageHeaderComponent } from '../../../shared/components/page-header/page-header.component';
import { ConfirmationModalComponent } from '../../../shared/components/confirmation-modal/confirmation-modal.component';
import { StatusColorPipe } from '../../../shared/pipes/status-color.pipe';
import { CurrencyFormatPipe } from '../../../shared/pipes/currency-format.pipe';
import { PaymentService } from '../../../core/services';
import { Payment, PaymentFilter, PaymentStatus, PaymentMethod, PaginatedResponse } from '../../../core/models';

@Component({
  selector: 'app-payment-list',
  standalone: true,
  imports: [
    CommonModule,
    RouterModule,
    ReactiveFormsModule,
    NzTableModule,
    NzButtonModule,
    NzInputModule,
    NzSelectModule,
    NzTagModule,
    NzCardModule,
    NzFormModule,
    NzIconModule,
    NzDividerModule,
    NzDatePickerModule,
    NzInputNumberModule,
    PageHeaderComponent,
    ConfirmationModalComponent,
    StatusColorPipe,
    CurrencyFormatPipe
  ],
  templateUrl: './payment-list.component.html',
  styleUrls: ['./payment-list.component.scss']
})
export class PaymentListComponent implements OnInit {
  payments: Payment[] = [];
  loading = false;
  total = 0;
  pageSize = 10;
  pageIndex = 1;
  
  filterForm!: FormGroup;
  private filterSubject = new BehaviorSubject<PaymentFilter>({});
  
  deleteModalVisible = false;
  deleteLoading = false;
  paymentToDelete: Payment | null = null;
  
  paymentStatusOptions = Object.values(PaymentStatus);
  paymentMethodOptions = Object.values(PaymentMethod);

  constructor(
    private fb: FormBuilder,
    private paymentService: PaymentService,
    private message: NzMessageService
  ) {}

  ngOnInit(): void {
    this.initFilterForm();
    this.setupFilterSubscription();
    this.loadPayments();
  }

  private initFilterForm(): void {
    this.filterForm = this.fb.group({
      contractId: [null],
      paymentMethod: [null],
      status: [null],
      startDate: [null],
      endDate: [null],
      minAmount: [null],
      maxAmount: [null]
    });
  }

  private setupFilterSubscription(): void {
    this.filterForm.valueChanges.pipe(
      debounceTime(300),
      distinctUntilChanged()
    ).subscribe(values => {
      const filter: PaymentFilter = {
        ...values,
        page: 1,
        limit: this.pageSize
      };
      this.filterSubject.next(filter);
      this.pageIndex = 1;
    });

    this.filterSubject.pipe(
      switchMap(filter => {
        this.loading = true;
        return this.paymentService.getPayments(filter);
      })
    ).subscribe({
      next: (response: PaginatedResponse<Payment>) => {
        this.payments = response.data;
        this.total = response.total;
        this.loading = false;
      },
      error: (error) => {
        this.message.error('Failed to load payments');
        this.loading = false;
      }
    });
  }

  private loadPayments(): void {
    const filter: PaymentFilter = {
      ...this.filterForm.value,
      page: this.pageIndex,
      limit: this.pageSize
    };
    this.filterSubject.next(filter);
  }

  onPageChange(pageIndex: number): void {
    this.pageIndex = pageIndex;
    this.loadPayments();
  }

  onPageSizeChange(pageSize: number): void {
    this.pageSize = pageSize;
    this.pageIndex = 1;
    this.loadPayments();
  }

  resetFilters(): void {
    this.filterForm.reset();
  }

  showDeleteModal(payment: Payment): void {
    this.paymentToDelete = payment;
    this.deleteModalVisible = true;
  }

  confirmDelete(): void {
    if (this.paymentToDelete) {
      this.deleteLoading = true;
      this.paymentService.deletePayment(this.paymentToDelete.id).subscribe({
        next: () => {
          this.message.success('Payment deleted successfully');
          this.deleteModalVisible = false;
          this.deleteLoading = false;
          this.paymentToDelete = null;
          this.loadPayments();
        },
        error: (error) => {
          this.message.error('Failed to delete payment');
          this.deleteLoading = false;
        }
      });
    }
  }

  cancelDelete(): void {
    this.deleteModalVisible = false;
    this.paymentToDelete = null;
  }

  generateReceipt(payment: Payment): void {
    this.paymentService.generatePaymentReceipt(payment.id).subscribe({
      next: (blob) => {
        const url = window.URL.createObjectURL(blob);
        const link = document.createElement('a');
        link.href = url;
        link.download = `payment-receipt-${payment.id}.pdf`;
        link.click();
        window.URL.revokeObjectURL(url);
      },
      error: (error) => {
        this.message.error('Failed to generate receipt');
      }
    });
  }

  getPaymentMethodIcon(method: PaymentMethod): string {
    const iconMap: { [key in PaymentMethod]: string } = {
      [PaymentMethod.CASH]: 'dollar',
      [PaymentMethod.CREDIT_CARD]: 'credit-card',
      [PaymentMethod.DEBIT_CARD]: 'credit-card',
      [PaymentMethod.BANK_TRANSFER]: 'bank',
      [PaymentMethod.DIGITAL_WALLET]: 'mobile'
    };
    return iconMap[method] || 'dollar';
  }
}
