import { Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { Observable } from 'rxjs';
import { map } from 'rxjs/operators';
import { BaseHttpService } from './base-http.service';
import { 
  Payment, 
  CreatePaymentRequest, 
  UpdatePaymentRequest,
  PaymentFilter,
  PaymentStatus,
  ApiResponse,
  PaginatedResponse
} from '../models';
import { environment } from '../../../environments/environment';

@Injectable({
  providedIn: 'root'
})
export class PaymentService extends BaseHttpService {
  protected baseUrl = environment.payment_service;

  constructor(httpClient: HttpClient) {
    super(httpClient);
  }

  getPayments(filter?: PaymentFilter): Observable<PaginatedResponse<Payment>> {
    return this.getPaginated<Payment>('/payments', filter).pipe(
      map(response => response.data)
    );
  }

  getPayment(id: number): Observable<Payment> {
    return this.get<Payment>(`/payments/${id}`).pipe(
      map(response => response.data)
    );
  }

  createPayment(payment: CreatePaymentRequest): Observable<Payment> {
    return this.post<Payment>('/payments', payment).pipe(
      map(response => response.data)
    );
  }

  updatePayment(payment: UpdatePaymentRequest): Observable<Payment> {
    return this.put<Payment>(`/payments/${payment.id}`, payment).pipe(
      map(response => response.data)
    );
  }

  updatePaymentStatus(id: number, status: PaymentStatus): Observable<Payment> {
    return this.put<Payment>(`/payments/${id}/status`, { status }).pipe(
      map(response => response.data)
    );
  }

  deletePayment(id: number): Observable<boolean> {
    return this.delete<any>(`/payments/${id}`).pipe(
      map(response => response.success)
    );
  }

  getPaymentsByContract(contractId: number): Observable<Payment[]> {
    return this.get<Payment[]>(`/payments/contract/${contractId}`).pipe(
      map(response => response.data)
    );
  }

  generatePaymentReceipt(id: number): Observable<Blob> {
    return this.downloadFile(`/payments/${id}/receipt`);
  }

  getPaymentSummary(startDate: Date, endDate: Date): Observable<any> {
    const params = { startDate, endDate };
    return this.get<any>('/payments/summary', params).pipe(
      map(response => response.data)
    );
  }
}
