import { Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { Observable } from 'rxjs';
import { map } from 'rxjs/operators';
import { BaseHttpService } from './base-http.service';
import { 
  Customer, 
  CreateCustomerRequest, 
  UpdateCustomerRequest,
  CustomerFilter,
  ApiResponse,
  PaginatedResponse
} from '../models';
import { environment } from '../../../environments/environment';

@Injectable({
  providedIn: 'root'
})
export class CustomerService extends BaseHttpService {
  protected baseUrl = environment.customer_service;

  constructor(httpClient: HttpClient) {
    super(httpClient);
  }

  getCustomers(filter?: CustomerFilter): Observable<PaginatedResponse<Customer>> {
    return this.getPaginated<Customer>('/customers', filter).pipe(
      map(response => response.data)
    );
  }

  getCustomer(id: number): Observable<Customer> {
    return this.get<Customer>(`/customers/${id}`).pipe(
      map(response => response.data)
    );
  }

  createCustomer(customer: CreateCustomerRequest): Observable<Customer> {
    return this.post<Customer>('/customers', customer).pipe(
      map(response => response.data)
    );
  }

  updateCustomer(customer: UpdateCustomerRequest): Observable<Customer> {
    return this.put<Customer>(`/customers/${customer.id}`, customer).pipe(
      map(response => response.data)
    );
  }

  deleteCustomer(id: number): Observable<boolean> {
    return this.delete<any>(`/customers/${id}`).pipe(
      map(response => response.success)
    );
  }

  searchCustomers(query: string): Observable<Customer[]> {
    return this.get<Customer[]>('/customers/search', { query }).pipe(
      map(response => response.data)
    );
  }
}
