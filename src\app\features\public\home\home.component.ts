import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { RouterModule } from '@angular/router';
import { NzButtonModule } from 'ng-zorro-antd/button';
import { NzCardModule } from 'ng-zorro-antd/card';
import { NzIconModule } from 'ng-zorro-antd/icon';
import { NzGridModule } from 'ng-zorro-antd/grid';

@Component({
  selector: 'app-home',
  standalone: true,
  imports: [
    CommonModule,
    RouterModule,
    NzButtonModule,
    NzCardModule,
    NzIconModule,
    NzGridModule
  ],
  templateUrl: './home.component.html',
  styleUrls: ['./home.component.scss']
})
export class HomeComponent implements OnInit {

  features = [
    {
      icon: 'car',
      title: 'Wide Selection',
      description: 'Choose from our diverse fleet of well-maintained vehicles'
    },
    {
      icon: 'safety-certificate',
      title: 'Fully Insured',
      description: 'All vehicles are fully insured for your peace of mind'
    },
    {
      icon: 'clock-circle',
      title: '24/7 Support',
      description: 'Round-the-clock customer support for any assistance'
    },
    {
      icon: 'dollar',
      title: 'Best Prices',
      description: 'Competitive pricing with no hidden fees'
    }
  ];

  constructor() { }

  ngOnInit(): void {
  }

}
