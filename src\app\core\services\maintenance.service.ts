import { Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { Observable } from 'rxjs';
import { map } from 'rxjs/operators';
import { BaseHttpService } from './base-http.service';
import { 
  MaintenanceHistory, 
  CreateMaintenanceRequest, 
  UpdateMaintenanceRequest,
  MaintenanceFilter,
  MaintenanceStatus,
  ApiResponse,
  PaginatedResponse
} from '../models';
import { environment } from '../../../environments/environment';

@Injectable({
  providedIn: 'root'
})
export class MaintenanceService extends BaseHttpService {
  protected baseUrl = environment.maintenance_service;

  constructor(httpClient: HttpClient) {
    super(httpClient);
  }

  getMaintenanceHistory(filter?: MaintenanceFilter): Observable<PaginatedResponse<MaintenanceHistory>> {
    return this.getPaginated<MaintenanceHistory>('/maintenance', filter).pipe(
      map(response => response.data)
    );
  }

  getMaintenance(id: number): Observable<MaintenanceHistory> {
    return this.get<MaintenanceHistory>(`/maintenance/${id}`).pipe(
      map(response => response.data)
    );
  }

  createMaintenance(maintenance: CreateMaintenanceRequest): Observable<MaintenanceHistory> {
    return this.post<MaintenanceHistory>('/maintenance', maintenance).pipe(
      map(response => response.data)
    );
  }

  updateMaintenance(maintenance: UpdateMaintenanceRequest): Observable<MaintenanceHistory> {
    return this.put<MaintenanceHistory>(`/maintenance/${maintenance.id}`, maintenance).pipe(
      map(response => response.data)
    );
  }

  updateMaintenanceStatus(id: number, status: MaintenanceStatus): Observable<MaintenanceHistory> {
    return this.put<MaintenanceHistory>(`/maintenance/${id}/status`, { status }).pipe(
      map(response => response.data)
    );
  }

  deleteMaintenance(id: number): Observable<boolean> {
    return this.delete<any>(`/maintenance/${id}`).pipe(
      map(response => response.success)
    );
  }

  getMaintenanceByVehicle(vehicleId: number): Observable<MaintenanceHistory[]> {
    return this.get<MaintenanceHistory[]>(`/maintenance/vehicle/${vehicleId}`).pipe(
      map(response => response.data)
    );
  }

  getUpcomingMaintenance(): Observable<MaintenanceHistory[]> {
    return this.get<MaintenanceHistory[]>('/maintenance/upcoming').pipe(
      map(response => response.data)
    );
  }

  getMaintenanceCostSummary(startDate: Date, endDate: Date): Observable<any> {
    const params = { startDate, endDate };
    return this.get<any>('/maintenance/cost-summary', params).pipe(
      map(response => response.data)
    );
  }
}
