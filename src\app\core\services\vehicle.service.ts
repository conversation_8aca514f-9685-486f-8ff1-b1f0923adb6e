import { Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { Observable } from 'rxjs';
import { map } from 'rxjs/operators';
import { BaseHttpService } from './base-http.service';
import { 
  Vehicle, 
  CreateVehicleRequest, 
  UpdateVehicleRequest,
  VehicleFilter,
  VehicleStatus,
  ApiResponse,
  PaginatedResponse
} from '../models';
import { environment } from '../../../environments/environment';

@Injectable({
  providedIn: 'root'
})
export class VehicleService extends BaseHttpService {
  protected baseUrl = environment.vehicle_service;

  constructor(httpClient: HttpClient) {
    super(httpClient);
  }

  getVehicles(filter?: VehicleFilter): Observable<PaginatedResponse<Vehicle>> {
    return this.getPaginated<Vehicle>('/vehicles', filter).pipe(
      map(response => response.data)
    );
  }

  getAvailableVehicles(startDate: Date, endDate: Date): Observable<Vehicle[]> {
    const params = { startDate, endDate };
    return this.get<Vehicle[]>('/vehicles/available', params).pipe(
      map(response => response.data)
    );
  }

  getVehicle(id: number): Observable<Vehicle> {
    return this.get<Vehicle>(`/vehicles/${id}`).pipe(
      map(response => response.data)
    );
  }

  createVehicle(vehicle: CreateVehicleRequest): Observable<Vehicle> {
    return this.post<Vehicle>('/vehicles', vehicle).pipe(
      map(response => response.data)
    );
  }

  updateVehicle(vehicle: UpdateVehicleRequest): Observable<Vehicle> {
    return this.put<Vehicle>(`/vehicles/${vehicle.id}`, vehicle).pipe(
      map(response => response.data)
    );
  }

  updateVehicleStatus(id: number, status: VehicleStatus): Observable<Vehicle> {
    return this.put<Vehicle>(`/vehicles/${id}/status`, { status }).pipe(
      map(response => response.data)
    );
  }

  deleteVehicle(id: number): Observable<boolean> {
    return this.delete<any>(`/vehicles/${id}`).pipe(
      map(response => response.success)
    );
  }

  uploadVehicleImage(id: number, file: File): Observable<string> {
    const formData = new FormData();
    formData.append('image', file);

    return this.post<{ imageUrl: string }>(`/vehicles/${id}/image`, formData).pipe(
      map(response => response.data.imageUrl)
    );
  }

}
