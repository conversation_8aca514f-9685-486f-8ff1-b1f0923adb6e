<app-page-header 
  title="Maintenance Management" 
  subtitle="Track vehicle maintenance history and schedule upcoming maintenance">
  
  <div nz-page-header-extra>
    <button nz-button nzType="primary" routerLink="/maintenance/create">
      <span nz-icon nzType="plus"></span>
      Schedule Maintenance
    </button>
  </div>
</app-page-header>

<nz-card>
  <form nz-form [formGroup]="filterForm" class="filter-form">
    <div nz-row [nzGutter]="[16, 16]">
      <div nz-col nzXs="24" nzSm="12" nzMd="6">
        <nz-form-item>
          <nz-form-label>Vehicle</nz-form-label>
          <nz-form-control>
            <nz-select formControlName="vehicleId" nzPlaceHolder="Select vehicle" nzAllowClear nzShowSearch>
              <nz-option 
                *ngFor="let vehicle of vehicles" 
                [nzValue]="vehicle.id" 
                [nzLabel]="vehicle.licensePlate + ' - ' + vehicle.vehicleType?.nameVehicleType">
              </nz-option>
            </nz-select>
          </nz-form-control>
        </nz-form-item>
      </div>
      
      <div nz-col nzXs="24" nzSm="12" nzMd="6">
        <nz-form-item>
          <nz-form-label>Status</nz-form-label>
          <nz-form-control>
            <nz-select formControlName="status" nzPlaceHolder="Select status" nzAllowClear>
              <nz-option *ngFor="let status of maintenanceStatusOptions" [nzValue]="status" [nzLabel]="status"></nz-option>
            </nz-select>
          </nz-form-control>
        </nz-form-item>
      </div>
      
      <div nz-col nzXs="24" nzSm="12" nzMd="6">
        <nz-form-item>
          <nz-form-label>Date From</nz-form-label>
          <nz-form-control>
            <nz-date-picker formControlName="startDate" nzPlaceHolder="Select date" style="width: 100%"></nz-date-picker>
          </nz-form-control>
        </nz-form-item>
      </div>
      
      <div nz-col nzXs="24" nzSm="12" nzMd="6">
        <nz-form-item>
          <nz-form-label>Date To</nz-form-label>
          <nz-form-control>
            <nz-date-picker formControlName="endDate" nzPlaceHolder="Select date" style="width: 100%"></nz-date-picker>
          </nz-form-control>
        </nz-form-item>
      </div>
      
      <div nz-col nzXs="24" nzSm="12" nzMd="6">
        <nz-form-item>
          <nz-form-label>Min Cost</nz-form-label>
          <nz-form-control>
            <nz-input-number formControlName="minCost" nzPlaceHolder="Min cost" [nzMin]="0" style="width: 100%"></nz-input-number>
          </nz-form-control>
        </nz-form-item>
      </div>
      
      <div nz-col nzXs="24" nzSm="12" nzMd="6">
        <nz-form-item>
          <nz-form-label>Max Cost</nz-form-label>
          <nz-form-control>
            <nz-input-number formControlName="maxCost" nzPlaceHolder="Max cost" [nzMin]="0" style="width: 100%"></nz-input-number>
          </nz-form-control>
        </nz-form-item>
      </div>
      
      <div nz-col nzXs="24" nzSm="12" nzMd="6">
        <nz-form-item>
          <nz-form-label>&nbsp;</nz-form-label>
          <nz-form-control>
            <button nz-button nzType="default" (click)="resetFilters()">
              <span nz-icon nzType="reload"></span>
              Reset
            </button>
          </nz-form-control>
        </nz-form-item>
      </div>
    </div>
  </form>
</nz-card>

<nz-card style="margin-top: 16px;">
  <nz-table 
    #maintenanceTable
    [nzData]="maintenanceRecords"
    [nzLoading]="loading"
    [nzTotal]="total"
    [nzPageSize]="pageSize"
    [nzPageIndex]="pageIndex"
    [nzShowSizeChanger]="true"
    [nzShowQuickJumper]="true"
    [nzShowTotal]="totalTemplate"
    (nzPageIndexChange)="onPageChange($event)"
    (nzPageSizeChange)="onPageSizeChange($event)">
    
    <thead>
      <tr>
        <th>Vehicle</th>
        <th>Maintenance Date</th>
        <th>Description</th>
        <th>Cost</th>
        <th>Status</th>
        <th>Actions</th>
      </tr>
    </thead>
    
    <tbody>
      <tr *ngFor="let maintenance of maintenanceRecords" [class.overdue]="isOverdue(maintenance)">
        <td>
          <div><strong>{{ maintenance.vehicle?.licensePlate }}</strong></div>
          <div style="color: #666; font-size: 12px;">{{ maintenance.vehicle?.vehicleType?.nameVehicleType }}</div>
        </td>
        <td>
          <div>{{ maintenance.maintenanceDate | date:'dd/MM/yyyy' }}</div>
          <div style="color: #666; font-size: 12px;" *ngIf="isOverdue(maintenance)">
            <span nz-icon nzType="exclamation-circle" style="color: #ff4d4f;"></span>
            Overdue
          </div>
        </td>
        <td>
          <div style="max-width: 200px; overflow: hidden; text-overflow: ellipsis;">
            {{ maintenance.description }}
          </div>
        </td>
        <td>
          <div><strong>{{ maintenance.cost | currencyFormat }}</strong></div>
        </td>
        <td>
          <nz-tag [nzColor]="maintenance.status | statusColor">
            <span nz-icon [nzType]="getStatusIcon(maintenance.status)"></span>
            {{ maintenance.status }}
          </nz-tag>
        </td>
        <td>
          <button nz-button nzType="link" nzSize="small" [routerLink]="['/maintenance/edit', maintenance.id]">
            <span nz-icon nzType="edit"></span>
            Edit
          </button>
          <nz-divider nzType="vertical"></nz-divider>
          <button nz-button nzType="link" nzSize="small" nzDanger (click)="showDeleteModal(maintenance)">
            <span nz-icon nzType="delete"></span>
            Delete
          </button>
        </td>
      </tr>
    </tbody>
  </nz-table>
  
  <ng-template #totalTemplate let-total let-range="range">
    {{ range[0] }}-{{ range[1] }} of {{ total }} maintenance records
  </ng-template>
</nz-card>

<app-confirmation-modal
  [(visible)]="deleteModalVisible"
  title="Delete Maintenance Record"
  content="Are you sure you want to delete this maintenance record? This action cannot be undone."
  okText="Delete"
  okType="primary"
  [loading]="deleteLoading"
  (onOk)="confirmDelete()"
  (onCancel)="cancelDelete()">
</app-confirmation-modal>
