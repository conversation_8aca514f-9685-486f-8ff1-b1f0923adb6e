import { BaseFilter } from './auth.model';

export interface Customer {
  id: number;
  fullName: string;
  email: string;
  phone: string;
  address: string;
  idCardNumber: string;
  driverLicenseNumber?: string;
  licenseIssueDate?: Date;
  licenseExpiryDate?: Date;
  createdAt: Date;
  updatedAt: Date;
}

export interface CreateCustomerRequest {
  fullName: string;
  email: string;
  phone: string;
  address: string;
  idCardNumber: string;
  driverLicenseNumber?: string;
  licenseIssueDate?: Date;
  licenseExpiryDate?: Date;
}

export interface UpdateCustomerRequest {
  id: number;
  fullName: string;
  email: string;
  phone: string;
  address: string;
  idCardNumber: string;
  driverLicenseNumber?: string;
  licenseIssueDate?: Date;
  licenseExpiryDate?: Date;
}

export interface CustomerFilter extends BaseFilter {
  search?: string;
  hasDriverLicense?: boolean;
}
