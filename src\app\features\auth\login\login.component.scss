.login-container {
  min-height: 100vh;
  display: flex;
  justify-content: center;
  align-items: center;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  padding: 20px;
}

.login-card {
  width: 100%;
  max-width: 400px;
}

.login-form-card {
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  border-radius: 12px;
  
  ::ng-deep .ant-card-head {
    text-align: center;
    border-bottom: 1px solid #f0f0f0;
    
    .ant-card-head-title {
      font-size: 24px;
      font-weight: 600;
      color: #1890ff;
    }
  }
  
  ::ng-deep .ant-card-body {
    padding: 32px;
  }
}

.login-button {
  width: 100%;
  height: 48px;
  font-size: 16px;
  font-weight: 500;
}

.password-toggle {
  cursor: pointer;
  color: #8c8c8c;
  
  &:hover {
    color: #1890ff;
  }
}

nz-form-item {
  margin-bottom: 24px;
  
  &:last-child {
    margin-bottom: 0;
  }
}

::ng-deep .ant-input-affix-wrapper {
  .ant-input-prefix {
    color: #8c8c8c;
  }
}
