<app-page-header 
  [title]="isEditMode ? 'Edit Maintenance' : 'Schedule Maintenance'" 
  [subtitle]="isEditMode ? 'Update maintenance record' : 'Schedule new vehicle maintenance'"
  [showBackButton]="true">
</app-page-header>

<nz-card [nzLoading]="loading">
  <form nz-form [formGroup]="maintenanceForm" (ngSubmit)="onSubmit()">
    <div nz-row [nzGutter]="[16, 16]">
      <div nz-col nzXs="24" nzSm="12">
        <nz-form-item>
          <nz-form-label [nzSpan]="24" nzRequired>Vehicle</nz-form-label>
          <nz-form-control [nzSpan]="24" nzErrorTip="Please select a vehicle">
            <nz-select formControlName="vehicleId" nzPlaceHolder="Select vehicle" nzShowSearch>
              <nz-option 
                *ngFor="let vehicle of vehicles" 
                [nzValue]="vehicle.id" 
                [nzLabel]="vehicle.licensePlate + ' - ' + vehicle.vehicleType?.nameVehicleType">
              </nz-option>
            </nz-select>
          </nz-form-control>
        </nz-form-item>
      </div>

      <div nz-col nzXs="24" nzSm="12">
        <nz-form-item>
          <nz-form-label [nzSpan]="24" nzRequired>Maintenance Date</nz-form-label>
          <nz-form-control [nzSpan]="24" nzErrorTip="Please select maintenance date">
            <nz-date-picker 
              formControlName="maintenanceDate" 
              nzPlaceHolder="Select maintenance date"
              style="width: 100%">
            </nz-date-picker>
          </nz-form-control>
        </nz-form-item>
      </div>

      <div nz-col nzXs="24" nzSm="12">
        <nz-form-item>
          <nz-form-label [nzSpan]="24" nzRequired>Cost (VND)</nz-form-label>
          <nz-form-control [nzSpan]="24" nzErrorTip="Please enter maintenance cost">
            <nz-input-number 
              formControlName="cost" 
              [nzMin]="0" 
              [nzStep]="100000"
              nzPlaceHolder="Enter maintenance cost"
              style="width: 100%">
            </nz-input-number>
          </nz-form-control>
        </nz-form-item>
      </div>

      <div nz-col nzXs="24" nzSm="12" *ngIf="isEditMode">
        <nz-form-item>
          <nz-form-label [nzSpan]="24">Status</nz-form-label>
          <nz-form-control [nzSpan]="24">
            <nz-select formControlName="status" nzPlaceHolder="Select status">
              <nz-option *ngFor="let status of maintenanceStatusOptions" [nzValue]="status" [nzLabel]="status"></nz-option>
            </nz-select>
          </nz-form-control>
        </nz-form-item>
      </div>

      <div nz-col nzSpan="24">
        <nz-form-item>
          <nz-form-label [nzSpan]="24">Maintenance Type (Quick Select)</nz-form-label>
          <nz-form-control [nzSpan]="24">
            <nz-select 
              nzPlaceHolder="Select maintenance type to add to description" 
              (ngModelChange)="onMaintenanceTypeSelect($event)"
              [ngModel]="null">
              <nz-option 
                *ngFor="let type of getMaintenanceTypeOptions()" 
                [nzValue]="type" 
                [nzLabel]="type">
              </nz-option>
            </nz-select>
          </nz-form-control>
        </nz-form-item>
      </div>

      <div nz-col nzSpan="24">
        <nz-form-item>
          <nz-form-label [nzSpan]="24" nzRequired>Description</nz-form-label>
          <nz-form-control [nzSpan]="24" nzErrorTip="Please enter maintenance description (min 10 characters)">
            <textarea 
              nz-input 
              formControlName="description" 
              placeholder="Describe the maintenance work to be performed..." 
              rows="4">
            </textarea>
          </nz-form-control>
        </nz-form-item>
      </div>
    </div>

    <div nz-row>
      <div nz-col nzSpan="24" class="form-actions">
        <button nz-button nzType="default" (click)="onCancel()" [disabled]="submitting">
          Cancel
        </button>
        <button 
          nz-button 
          nzType="primary" 
          [nzLoading]="submitting"
          [disabled]="!maintenanceForm.valid"
          style="margin-left: 8px;">
          {{ isEditMode ? 'Update' : 'Schedule' }}
        </button>
      </div>
    </div>
  </form>
</nz-card>
