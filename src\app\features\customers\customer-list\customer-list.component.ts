import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { RouterModule } from '@angular/router';
import { ReactiveFormsModule, FormBuilder, FormGroup } from '@angular/forms';
import { NzTableModule } from 'ng-zorro-antd/table';
import { NzButtonModule } from 'ng-zorro-antd/button';
import { NzInputModule } from 'ng-zorro-antd/input';
import { NzSelectModule } from 'ng-zorro-antd/select';
import { NzTagModule } from 'ng-zorro-antd/tag';
import { NzCardModule } from 'ng-zorro-antd/card';
import { NzFormModule } from 'ng-zorro-antd/form';
import { NzIconModule } from 'ng-zorro-antd/icon';
import { NzDividerModule } from 'ng-zorro-antd/divider';
import { NzMessageService } from 'ng-zorro-antd/message';
import { Observable, BehaviorSubject } from 'rxjs';
import { debounceTime, distinctUntilChanged, switchMap } from 'rxjs/operators';

import { PageHeaderComponent } from '../../../shared/components/page-header/page-header.component';
import { ConfirmationModalComponent } from '../../../shared/components/confirmation-modal/confirmation-modal.component';
import { CustomerService } from '../../../core/services';
import { Customer, CustomerFilter, PaginatedResponse } from '../../../core/models';

@Component({
  selector: 'app-customer-list',
  standalone: true,
  imports: [
    CommonModule,
    RouterModule,
    ReactiveFormsModule,
    NzTableModule,
    NzButtonModule,
    NzInputModule,
    NzSelectModule,
    NzTagModule,
    NzCardModule,
    NzFormModule,
    NzIconModule,
    NzDividerModule,
    PageHeaderComponent,
    ConfirmationModalComponent
  ],
  templateUrl: './customer-list.component.html',
  styleUrls: ['./customer-list.component.scss']
})
export class CustomerListComponent implements OnInit {
  customers: Customer[] = [];
  loading = false;
  total = 0;
  pageSize = 10;
  pageIndex = 1;
  
  filterForm!: FormGroup;
  private filterSubject = new BehaviorSubject<CustomerFilter>({});
  
  deleteModalVisible = false;
  deleteLoading = false;
  customerToDelete: Customer | null = null;

  constructor(
    private fb: FormBuilder,
    private customerService: CustomerService,
    private message: NzMessageService
  ) {}

  ngOnInit(): void {
    this.initFilterForm();
    this.setupFilterSubscription();
    this.loadCustomers();
  }

  private initFilterForm(): void {
    this.filterForm = this.fb.group({
      search: [''],
      hasDriverLicense: [null]
    });
  }

  private setupFilterSubscription(): void {
    this.filterForm.valueChanges.pipe(
      debounceTime(300),
      distinctUntilChanged()
    ).subscribe(values => {
      const filter: CustomerFilter = {
        ...values,
        page: 1,
        limit: this.pageSize
      };
      this.filterSubject.next(filter);
      this.pageIndex = 1;
    });

    this.filterSubject.pipe(
      switchMap(filter => {
        this.loading = true;
        return this.customerService.getCustomers(filter);
      })
    ).subscribe({
      next: (response: PaginatedResponse<Customer>) => {
        this.customers = response.data;
        this.total = response.total;
        this.loading = false;
      },
      error: (error) => {
        this.message.error('Failed to load customers');
        this.loading = false;
      }
    });
  }

  private loadCustomers(): void {
    const filter: CustomerFilter = {
      ...this.filterForm.value,
      page: this.pageIndex,
      limit: this.pageSize
    };
    this.filterSubject.next(filter);
  }

  onPageChange(pageIndex: number): void {
    this.pageIndex = pageIndex;
    this.loadCustomers();
  }

  onPageSizeChange(pageSize: number): void {
    this.pageSize = pageSize;
    this.pageIndex = 1;
    this.loadCustomers();
  }

  resetFilters(): void {
    this.filterForm.reset();
  }

  showDeleteModal(customer: Customer): void {
    this.customerToDelete = customer;
    this.deleteModalVisible = true;
  }

  confirmDelete(): void {
    if (this.customerToDelete) {
      this.deleteLoading = true;
      this.customerService.deleteCustomer(this.customerToDelete.id).subscribe({
        next: () => {
          this.message.success('Customer deleted successfully');
          this.deleteModalVisible = false;
          this.deleteLoading = false;
          this.customerToDelete = null;
          this.loadCustomers();
        },
        error: (error) => {
          this.message.error('Failed to delete customer');
          this.deleteLoading = false;
        }
      });
    }
  }

  cancelDelete(): void {
    this.deleteModalVisible = false;
    this.customerToDelete = null;
  }

  hasDriverLicense(customer: Customer): boolean {
    return !!(customer.driverLicenseNumber && customer.licenseExpiryDate);
  }

  isLicenseExpired(customer: Customer): boolean {
    if (!customer.licenseExpiryDate) return false;
    return new Date(customer.licenseExpiryDate) < new Date();
  }
}
