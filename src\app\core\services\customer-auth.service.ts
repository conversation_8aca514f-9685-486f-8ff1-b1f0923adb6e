import { Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { BehaviorSubject, Observable, of } from 'rxjs';
import { map, tap, catchError } from 'rxjs/operators';
import { 
  User, 
  LoginRequest, 
  LoginResponse, 
  CustomerRegistrationRequest,
  ApiResponse,
  UserRole
} from '../models';
import { environment } from '../../../environments/environment';

@Injectable({
  providedIn: 'root'
})
export class CustomerAuthService {
  private currentCustomerSubject = new BehaviorSubject<User | null>(null);
  private tokenSubject = new BehaviorSubject<string | null>(null);

  public currentCustomer$ = this.currentCustomerSubject.asObservable();
  public token$ = this.tokenSubject.asObservable();

  constructor(private httpClient: HttpClient) {
    this.loadStoredAuth();
  }

  register(registrationData: CustomerRegistrationRequest): Observable<LoginResponse> {
    return this.httpClient.post<ApiResponse<LoginResponse>>(`${environment.auth_service}/customer/register`, registrationData).pipe(
      map(response => response.data),
      tap(loginResponse => {
        this.setAuthData(loginResponse);
      })
    );
  }

  login(credentials: LoginRequest): Observable<LoginResponse> {
    return this.httpClient.post<ApiResponse<LoginResponse>>(`${environment.auth_service}/customer/login`, credentials).pipe(
      map(response => response.data),
      tap(loginResponse => {
        this.setAuthData(loginResponse);
      })
    );
  }

  logout(): Observable<boolean> {
    return this.httpClient.post<ApiResponse<any>>(`${environment.auth_service}/customer/logout`, {}).pipe(
      map(() => {
        this.clearAuthData();
        return true;
      }),
      catchError(() => {
        this.clearAuthData();
        return of(true);
      })
    );
  }

  refreshToken(): Observable<LoginResponse> {
    const refreshToken = this.getRefreshToken();
    if (!refreshToken) {
      throw new Error('No refresh token available');
    }

    return this.httpClient.post<ApiResponse<LoginResponse>>(`${environment.auth_service}/customer/refresh`, { refreshToken }).pipe(
      map(response => response.data),
      tap(loginResponse => {
        this.setAuthData(loginResponse);
      })
    );
  }

  isAuthenticated(): boolean {
    const token = this.getToken();
    if (!token) return false;
    
    try {
      const payload = JSON.parse(atob(token.split('.')[1]));
      const currentTime = Math.floor(Date.now() / 1000);
      return payload.exp > currentTime && payload.role === UserRole.CUSTOMER;
    } catch {
      return false;
    }
  }

  getToken(): string | null {
    return this.tokenSubject.value || localStorage.getItem('customer_token');
  }

  getRefreshToken(): string | null {
    return localStorage.getItem('customer_refreshToken');
  }

  getCurrentCustomer(): User | null {
    return this.currentCustomerSubject.value;
  }

  updateProfile(profileData: Partial<User>): Observable<User> {
    return this.httpClient.put<ApiResponse<User>>(`${environment.auth_service}/customer/profile`, profileData).pipe(
      map(response => response.data),
      tap(user => {
        this.currentCustomerSubject.next(user);
        localStorage.setItem('customer_user', JSON.stringify(user));
      })
    );
  }

  private setAuthData(loginResponse: LoginResponse): void {
    localStorage.setItem('customer_token', loginResponse.token);
    localStorage.setItem('customer_refreshToken', loginResponse.refreshToken);
    localStorage.setItem('customer_user', JSON.stringify(loginResponse.user));
    
    this.tokenSubject.next(loginResponse.token);
    this.currentCustomerSubject.next(loginResponse.user);
  }

  private clearAuthData(): void {
    localStorage.removeItem('customer_token');
    localStorage.removeItem('customer_refreshToken');
    localStorage.removeItem('customer_user');
    
    this.tokenSubject.next(null);
    this.currentCustomerSubject.next(null);
  }

  private loadStoredAuth(): void {
    const token = localStorage.getItem('customer_token');
    const userStr = localStorage.getItem('customer_user');
    
    if (token && userStr && this.isAuthenticated()) {
      try {
        const user = JSON.parse(userStr);
        this.tokenSubject.next(token);
        this.currentCustomerSubject.next(user);
      } catch {
        this.clearAuthData();
      }
    } else {
      this.clearAuthData();
    }
  }
}
