<div class="dashboard-container">
  <!-- Welcome Section -->
  <div class="welcome-section">
    <div class="welcome-content">
      <h1>{{ getWelcomeMessage() }}, {{ currentUser?.fullName }}!</h1>
      <p>Welcome back to your car rental dashboard</p>
    </div>
    <div class="quick-actions">
      <button nz-button nzType="primary" nzSize="large" (click)="browseVehicles()">
        <span nz-icon nzType="car"></span>
        Browse Vehicles
      </button>
    </div>
  </div>

  <!-- Statistics Cards -->
  <div class="stats-section">
    <div nz-row [nzGutter]="[16, 16]">
      <div nz-col nzXs="12" nzSm="6">
        <nz-card class="stat-card">
          <nz-statistic
            nzTitle="Total Bookings"
            [nzValue]="stats.totalBookings"
            [nzValueStyle]="{ color: '#1890ff' }"
            [nzPrefix]="totalIconTemplate">
          </nz-statistic>
          <ng-template #totalIconTemplate>
            <span nz-icon nzType="calendar"></span>
          </ng-template>
        </nz-card>
      </div>
      
      <div nz-col nzXs="12" nzSm="6">
        <nz-card class="stat-card">
          <nz-statistic
            nzTitle="Active Bookings"
            [nzValue]="stats.activeBookings"
            [nzValueStyle]="{ color: '#52c41a' }"
            [nzPrefix]="activeIconTemplate">
          </nz-statistic>
          <ng-template #activeIconTemplate>
            <span nz-icon nzType="clock-circle"></span>
          </ng-template>
        </nz-card>
      </div>
      
      <div nz-col nzXs="12" nzSm="6">
        <nz-card class="stat-card">
          <nz-statistic
            nzTitle="Completed"
            [nzValue]="stats.completedBookings"
            [nzValueStyle]="{ color: '#722ed1' }"
            [nzPrefix]="completedIconTemplate">
          </nz-statistic>
          <ng-template #completedIconTemplate>
            <span nz-icon nzType="check-circle"></span>
          </ng-template>
        </nz-card>
      </div>
      
      <div nz-col nzXs="12" nzSm="6">
        <nz-card class="stat-card">
          <nz-statistic
            nzTitle="Total Spent"
            [nzValue]="stats.totalSpent | currencyFormat"
            [nzValueStyle]="{ color: '#fa8c16' }"
            [nzPrefix]="spentIconTemplate">
          </nz-statistic>
          <ng-template #spentIconTemplate>
            <span nz-icon nzType="dollar"></span>
          </ng-template>
        </nz-card>
      </div>
    </div>
  </div>

  <!-- Recent Bookings -->
  <div class="recent-bookings-section">
    <nz-card class="bookings-card">
      <div class="card-header">
        <h2>Recent Bookings</h2>
        <button nz-button nzType="link" (click)="viewAllBookings()">
          View All
          <span nz-icon nzType="arrow-right"></span>
        </button>
      </div>

      <div *ngIf="loading" class="loading-container">
        <nz-spin nzSize="large" nzTip="Loading your bookings..."></nz-spin>
      </div>

      <div *ngIf="!loading && recentBookings.length > 0">
        <nz-table [nzData]="recentBookings" [nzShowPagination]="false">
          <thead>
            <tr>
              <th>Contract</th>
              <th>Vehicle</th>
              <th>Rental Period</th>
              <th>Status</th>
              <th>Amount</th>
              <th>Actions</th>
            </tr>
          </thead>
          <tbody>
            <tr *ngFor="let booking of recentBookings">
              <td>
                <div class="contract-info">
                  <div class="contract-number">{{ booking.contractNumber }}</div>
                  <div class="contract-type">{{ booking.rentalType === 'SELF_DRIVE' ? 'Self Drive' : 'With Driver' }}</div>
                </div>
              </td>
              <td>
                <div class="vehicle-info">
                  <div class="vehicle-name">{{ booking.vehicle?.vehicleType?.nameVehicleType }}</div>
                  <div class="vehicle-plate">{{ booking.vehicle?.licensePlate }}</div>
                </div>
              </td>
              <td>
                <div class="rental-period">
                  <div>{{ booking.startDate | date:'dd/MM/yyyy' }} - {{ booking.endDate | date:'dd/MM/yyyy' }}</div>
                  <div class="duration">{{ getRentalDuration(booking) }} days</div>
                </div>
              </td>
              <td>
                <nz-tag [nzColor]="booking.status | statusColor">
                  {{ getBookingStatusText(booking.status) }}
                </nz-tag>
              </td>
              <td>
                <div class="amount">{{ booking.totalPrice | currencyFormat }}</div>
              </td>
              <td>
                <div class="actions">
                  <button nz-button nzType="link" nzSize="small">
                    <span nz-icon nzType="eye"></span>
                    View
                  </button>
                  <button 
                    *ngIf="canCancelBooking(booking)"
                    nz-button 
                    nzType="link" 
                    nzSize="small" 
                    nzDanger>
                    <span nz-icon nzType="close"></span>
                    Cancel
                  </button>
                </div>
              </td>
            </tr>
          </tbody>
        </nz-table>
      </div>

      <div *ngIf="!loading && recentBookings.length === 0" class="empty-bookings">
        <nz-empty 
          nzNotFoundImage="simple" 
          nzNotFoundContent="No bookings found">
          <ng-container nzNotFoundFooter>
            <button nz-button nzType="primary" (click)="browseVehicles()">
              Make Your First Booking
            </button>
          </ng-container>
        </nz-empty>
      </div>
    </nz-card>
  </div>

  <!-- Quick Links -->
  <div class="quick-links-section">
    <div nz-row [nzGutter]="[16, 16]">
      <div nz-col nzXs="24" nzSm="12" nzMd="8">
        <nz-card class="quick-link-card" (click)="browseVehicles()">
          <div class="quick-link-content">
            <span nz-icon nzType="car" class="quick-link-icon"></span>
            <h3>Browse Vehicles</h3>
            <p>Find the perfect car for your next trip</p>
          </div>
        </nz-card>
      </div>
      
      <div nz-col nzXs="24" nzSm="12" nzMd="8">
        <nz-card class="quick-link-card" routerLink="/customer/bookings">
          <div class="quick-link-content">
            <span nz-icon nzType="calendar" class="quick-link-icon"></span>
            <h3>My Bookings</h3>
            <p>View and manage your rental history</p>
          </div>
        </nz-card>
      </div>
      
      <div nz-col nzXs="24" nzSm="12" nzMd="8">
        <nz-card class="quick-link-card" routerLink="/customer/profile">
          <div class="quick-link-content">
            <span nz-icon nzType="user" class="quick-link-icon"></span>
            <h3>My Profile</h3>
            <p>Update your personal information</p>
          </div>
        </nz-card>
      </div>
    </div>
  </div>
</div>
