<app-page-header 
  [title]="isEditMode ? 'Edit Contract' : 'New Contract'" 
  [subtitle]="isEditMode ? 'Update contract information' : 'Create a new rental contract'"
  [showBackButton]="true">
</app-page-header>

<nz-card [nzLoading]="loading">
  <form nz-form [formGroup]="contractForm" (ngSubmit)="onSubmit()">
    <div nz-row [nzGutter]="[16, 16]">
      <div nz-col nzXs="24" nzSm="12">
        <nz-form-item>
          <nz-form-label [nzSpan]="24" nzRequired>Contract Number</nz-form-label>
          <nz-form-control [nzSpan]="24" nzErrorTip="Please enter contract number (format: CR123456)">
            <input nz-input formControlName="contractNumber" placeholder="e.g., CR202401001" [readonly]="!isEditMode">
          </nz-form-control>
        </nz-form-item>
      </div>

      <div nz-col nzXs="24" nzSm="12">
        <nz-form-item>
          <nz-form-label [nzSpan]="24" nzRequired>Customer</nz-form-label>
          <nz-form-control [nzSpan]="24" nzErrorTip="Please select a customer">
            <nz-select formControlName="customerId" nzPlaceHolder="Select customer" nzShowSearch>
              <nz-option *ngFor="let customer of customers" [nzValue]="customer.id" [nzLabel]="customer.fullName + ' - ' + customer.phone"></nz-option>
            </nz-select>
          </nz-form-control>
        </nz-form-item>
      </div>

      <div nz-col nzSpan="24">
        <nz-form-item>
          <nz-form-label [nzSpan]="24" nzRequired>Rental Type</nz-form-label>
          <nz-form-control [nzSpan]="24">
            <nz-radio-group formControlName="rentalType">
              <label nz-radio nzValue="SELF_DRIVE">Self Drive</label>
              <label nz-radio nzValue="WITH_DRIVER">With Driver</label>
            </nz-radio-group>
          </nz-form-control>
        </nz-form-item>
      </div>

      <div nz-col nzXs="24" nzSm="12">
        <nz-form-item>
          <nz-form-label [nzSpan]="24" nzRequired>Start Date</nz-form-label>
          <nz-form-control [nzSpan]="24" nzErrorTip="Please select start date">
            <nz-date-picker 
              formControlName="startDate" 
              [nzDisabledDate]="disabledDate"
              nzPlaceHolder="Select start date"
              style="width: 100%"
              (ngModelChange)="calculateTotalPrice()">
            </nz-date-picker>
          </nz-form-control>
        </nz-form-item>
      </div>

      <div nz-col nzXs="24" nzSm="12">
        <nz-form-item>
          <nz-form-label [nzSpan]="24" nzRequired>End Date</nz-form-label>
          <nz-form-control [nzSpan]="24" nzErrorTip="Please select end date">
            <nz-date-picker 
              formControlName="endDate" 
              [nzDisabledDate]="disabledDate"
              nzPlaceHolder="Select end date"
              style="width: 100%"
              (ngModelChange)="calculateTotalPrice()">
            </nz-date-picker>
          </nz-form-control>
        </nz-form-item>
      </div>

      <div nz-col nzXs="24" nzSm="12">
        <nz-form-item>
          <nz-form-label [nzSpan]="24" nzRequired>Vehicle</nz-form-label>
          <nz-form-control [nzSpan]="24" nzErrorTip="Please select a vehicle">
            <nz-select 
              formControlName="vehicleId" 
              nzPlaceHolder="Select vehicle" 
              nzShowSearch
              (ngModelChange)="calculateTotalPrice()">
              <nz-option 
                *ngFor="let vehicle of availableVehicles" 
                [nzValue]="vehicle.id" 
                [nzLabel]="vehicle.licensePlate + ' - ' + vehicle.vehicleType?.nameVehicleType + ' (' + (vehicle.basePrice | currency:'VND':'symbol':'1.0-0') + '/day)'">
              </nz-option>
            </nz-select>
          </nz-form-control>
        </nz-form-item>
      </div>

      <div nz-col nzXs="24" nzSm="12" *ngIf="contractForm.get('rentalType')?.value === 'WITH_DRIVER'">
        <nz-form-item>
          <nz-form-label [nzSpan]="24" nzRequired>Driver</nz-form-label>
          <nz-form-control [nzSpan]="24" nzErrorTip="Please select a driver">
            <nz-select formControlName="driverId" nzPlaceHolder="Select driver" nzShowSearch>
              <nz-option 
                *ngFor="let driver of availableDrivers" 
                [nzValue]="driver.id" 
                [nzLabel]="driver.fullName + ' - ' + driver.phone">
              </nz-option>
            </nz-select>
          </nz-form-control>
        </nz-form-item>
      </div>

      <div nz-col nzXs="24" nzSm="12">
        <nz-form-item>
          <nz-form-label [nzSpan]="24" nzRequired>Total Price (VND)</nz-form-label>
          <nz-form-control [nzSpan]="24" nzErrorTip="Please enter total price">
            <nz-input-number 
              formControlName="totalPrice" 
              [nzMin]="0" 
              [nzStep]="100000"
              nzPlaceHolder="Enter total price"
              style="width: 100%">
            </nz-input-number>
          </nz-form-control>
        </nz-form-item>
      </div>

      <div nz-col nzXs="24" nzSm="12">
        <nz-form-item>
          <nz-form-label [nzSpan]="24" nzRequired>Deposit Amount (VND)</nz-form-label>
          <nz-form-control [nzSpan]="24" nzErrorTip="Please enter deposit amount">
            <nz-input-number 
              formControlName="depositAmount" 
              [nzMin]="0" 
              [nzStep]="100000"
              nzPlaceHolder="Enter deposit amount"
              style="width: 100%">
            </nz-input-number>
          </nz-form-control>
        </nz-form-item>
      </div>

      <div nz-col nzXs="24" nzSm="12" *ngIf="isEditMode">
        <nz-form-item>
          <nz-form-label [nzSpan]="24">Status</nz-form-label>
          <nz-form-control [nzSpan]="24">
            <nz-select formControlName="status" nzPlaceHolder="Select status">
              <nz-option *ngFor="let status of contractStatusOptions" [nzValue]="status" [nzLabel]="status"></nz-option>
            </nz-select>
          </nz-form-control>
        </nz-form-item>
      </div>
    </div>

    <div nz-row>
      <div nz-col nzSpan="24" class="form-actions">
        <button nz-button nzType="default" (click)="onCancel()" [disabled]="submitting">
          Cancel
        </button>
        <button 
          nz-button 
          nzType="primary" 
          [nzLoading]="submitting"
          [disabled]="!contractForm.valid"
          style="margin-left: 8px;">
          {{ isEditMode ? 'Update' : 'Create' }}
        </button>
      </div>
    </div>
  </form>
</nz-card>
