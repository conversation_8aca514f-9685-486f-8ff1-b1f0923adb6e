<div class="vehicle-details-container" *ngIf="vehicle">
  <!-- Header -->
  <div class="page-header">
    <button nz-button nzType="link" (click)="goBack()" class="back-button">
      <span nz-icon nzType="arrow-left"></span>
      Back to Vehicles
    </button>
    <h1>{{ vehicle.vehicleType?.nameVehicleType }}</h1>
    <div class="vehicle-status">
      <nz-tag nzColor="green">Available</nz-tag>
    </div>
  </div>

  <div nz-row [nzGutter]="[24, 24]">
    <!-- Vehicle Information -->
    <div nz-col nzXs="24" nzLg="14">
      <!-- Vehicle Images -->
      <nz-card class="image-card">
        <div class="vehicle-image-container">
          <img
            [src]="vehicle.imageUrl || '/assets/images/default-car.jpg'"
            [alt]="vehicle.vehicleType?.nameVehicleType || 'Vehicle'"
            (error)="$any($event.target).src='/assets/images/default-car.jpg'"
            class="main-image">
        </div>
      </nz-card>

      <!-- Vehicle Specifications -->
      <nz-card nzTitle="Vehicle Specifications" class="specs-card">
        <nz-descriptions nzBordered [nzColumn]="2">
          <nz-descriptions-item nzTitle="License Plate">
            {{ vehicle.licensePlate }}
          </nz-descriptions-item>
          <nz-descriptions-item nzTitle="Vehicle Type">
            {{ vehicle.vehicleType?.nameVehicleType }}
          </nz-descriptions-item>
          <nz-descriptions-item nzTitle="Seats">
            <span nz-icon nzType="user"></span>
            {{ vehicle.seatCount }} seats
          </nz-descriptions-item>
          <nz-descriptions-item nzTitle="Color">
            <span nz-icon nzType="bg-colors"></span>
            {{ vehicle.color }}
          </nz-descriptions-item>
          <nz-descriptions-item nzTitle="Daily Rate">
            {{ vehicle.basePrice | currencyFormat }}
          </nz-descriptions-item>
          <nz-descriptions-item nzTitle="Status">
            <nz-tag nzColor="green">{{ vehicle.status }}</nz-tag>
          </nz-descriptions-item>
        </nz-descriptions>
      </nz-card>

      <!-- Features -->
      <nz-card nzTitle="Features & Amenities" class="features-card">
        <div class="features-grid">
          <div class="feature-item" *ngFor="let feature of getVehicleFeatures()">
            <span nz-icon nzType="check-circle" class="feature-icon"></span>
            <span>{{ feature }}</span>
          </div>
        </div>
      </nz-card>

      <!-- Description -->
      <nz-card nzTitle="Description" class="description-card">
        <p>
          This {{ vehicle.vehicleType?.nameVehicleType }} is perfect for your rental needs. 
          With {{ vehicle.seatCount }} comfortable seats and modern amenities, 
          it provides a smooth and enjoyable driving experience. 
          The vehicle is well-maintained and regularly serviced to ensure your safety and comfort.
        </p>
      </nz-card>
    </div>

    <!-- Booking Panel -->
    <div nz-col nzXs="24" nzLg="10">
      <div class="booking-panel">
        <nz-card nzTitle="Book This Vehicle" class="booking-card">
          <form nz-form [formGroup]="bookingForm">
            <!-- Rental Dates -->
            <nz-form-item>
              <nz-form-label [nzSpan]="24" nzRequired>Pick-up Date</nz-form-label>
              <nz-form-control [nzSpan]="24" nzErrorTip="Please select pick-up date">
                <nz-date-picker 
                  formControlName="startDate" 
                  [nzDisabledDate]="disabledDate"
                  nzPlaceHolder="Select pick-up date"
                  style="width: 100%">
                </nz-date-picker>
              </nz-form-control>
            </nz-form-item>

            <nz-form-item>
              <nz-form-label [nzSpan]="24" nzRequired>Return Date</nz-form-label>
              <nz-form-control [nzSpan]="24" nzErrorTip="Please select return date">
                <nz-date-picker 
                  formControlName="endDate" 
                  [nzDisabledDate]="disabledEndDate"
                  nzPlaceHolder="Select return date"
                  style="width: 100%">
                </nz-date-picker>
              </nz-form-control>
            </nz-form-item>

            <!-- Rental Type -->
            <nz-form-item>
              <nz-form-label [nzSpan]="24" nzRequired>Rental Type</nz-form-label>
              <nz-form-control [nzSpan]="24">
                <nz-radio-group formControlName="rentalType">
                  <div class="rental-option">
                    <label nz-radio nzValue="SELF_DRIVE">
                      <div class="option-content">
                        <div class="option-title">Self Drive</div>
                        <div class="option-description">Drive the vehicle yourself</div>
                      </div>
                    </label>
                  </div>
                  <div class="rental-option">
                    <label nz-radio nzValue="WITH_DRIVER">
                      <div class="option-content">
                        <div class="option-title">With Driver</div>
                        <div class="option-description">Professional driver included (+500,000 VND/day)</div>
                      </div>
                    </label>
                  </div>
                </nz-radio-group>
              </nz-form-control>
            </nz-form-item>

            <nz-divider></nz-divider>

            <!-- Pricing Summary -->
            <div class="pricing-summary">
              <div class="pricing-row">
                <span>Rental Duration:</span>
                <span class="value">{{ calculateRentalDays() }} day(s)</span>
              </div>
              <div class="pricing-row">
                <span>Vehicle Cost:</span>
                <span class="value">{{ calculateBasePrice() | currencyFormat }}</span>
              </div>
              <div class="pricing-row" *ngIf="bookingForm.value.rentalType === 'WITH_DRIVER'">
                <span>Driver Cost:</span>
                <span class="value">{{ calculateDriverCost() | currencyFormat }}</span>
              </div>
              <nz-divider></nz-divider>
              <div class="pricing-row total">
                <span>Total Cost:</span>
                <span class="value">{{ calculateTotalPrice() | currencyFormat }}</span>
              </div>
            </div>

            <!-- Book Button -->
            <button 
              nz-button 
              nzType="primary" 
              nzSize="large"
              [disabled]="!bookingForm.valid"
              (click)="onBookNow()"
              class="book-button">
              Book Now
            </button>

            <!-- Additional Info -->
            <div class="additional-info">
              <div class="info-item">
                <span nz-icon nzType="safety-certificate"></span>
                <span>Fully insured</span>
              </div>
              <div class="info-item">
                <span nz-icon nzType="phone"></span>
                <span>24/7 support</span>
              </div>
              <div class="info-item">
                <span nz-icon nzType="reload"></span>
                <span>Free cancellation</span>
              </div>
            </div>
          </form>
        </nz-card>
      </div>
    </div>
  </div>
</div>

<!-- Loading State -->
<div *ngIf="loading" class="loading-container">
  <nz-spin nzSize="large" nzTip="Loading vehicle details..."></nz-spin>
</div>
