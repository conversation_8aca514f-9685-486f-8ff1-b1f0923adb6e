.dashboard-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 20px;
  background: #f5f5f5;
  min-height: 100vh;
}

.welcome-section {
  display: flex;
  justify-content: space-between;
  align-items: center;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  padding: 40px;
  border-radius: 12px;
  margin-bottom: 24px;
  
  .welcome-content {
    h1 {
      font-size: 32px;
      font-weight: 700;
      margin: 0 0 8px 0;
      color: white;
    }
    
    p {
      font-size: 16px;
      margin: 0;
      opacity: 0.9;
    }
  }
  
  .quick-actions {
    button {
      height: 48px;
      font-size: 16px;
      font-weight: 500;
    }
  }
}

.stats-section {
  margin-bottom: 32px;
  
  .stat-card {
    text-align: center;
    border-radius: 12px;
    transition: all 0.3s ease;
    
    &:hover {
      transform: translateY(-2px);
      box-shadow: 0 8px 24px rgba(0, 0, 0, 0.12);
    }
    
    ::ng-deep .ant-statistic {
      .ant-statistic-title {
        font-size: 14px;
        color: #666;
        margin-bottom: 8px;
      }
      
      .ant-statistic-content {
        font-size: 24px;
        font-weight: 600;
        
        .ant-statistic-content-prefix {
          margin-right: 8px;
        }
      }
    }
  }
}

.recent-bookings-section {
  margin-bottom: 32px;
  
  .bookings-card {
    border-radius: 12px;
    
    .card-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 24px;
      
      h2 {
        font-size: 24px;
        font-weight: 600;
        color: #333;
        margin: 0;
      }
      
      button {
        color: #1890ff;
        
        &:hover {
          color: #40a9ff;
        }
      }
    }
  }
}

.loading-container {
  text-align: center;
  padding: 60px 0;
}

.contract-info {
  .contract-number {
    font-weight: 600;
    color: #333;
  }
  
  .contract-type {
    font-size: 12px;
    color: #666;
    margin-top: 2px;
  }
}

.vehicle-info {
  .vehicle-name {
    font-weight: 500;
    color: #333;
  }
  
  .vehicle-plate {
    font-size: 12px;
    color: #1890ff;
    margin-top: 2px;
  }
}

.rental-period {
  .duration {
    font-size: 12px;
    color: #666;
    margin-top: 2px;
  }
}

.amount {
  font-weight: 600;
  color: #1890ff;
}

.actions {
  display: flex;
  gap: 8px;
}

.empty-bookings {
  padding: 60px 0;
  text-align: center;
}

.quick-links-section {
  .quick-link-card {
    border-radius: 12px;
    cursor: pointer;
    transition: all 0.3s ease;
    
    &:hover {
      transform: translateY(-4px);
      box-shadow: 0 12px 32px rgba(0, 0, 0, 0.15);
    }
    
    .quick-link-content {
      text-align: center;
      padding: 20px;
      
      .quick-link-icon {
        font-size: 48px;
        color: #1890ff;
        margin-bottom: 16px;
        display: block;
      }
      
      h3 {
        font-size: 18px;
        font-weight: 600;
        color: #333;
        margin: 0 0 8px 0;
      }
      
      p {
        font-size: 14px;
        color: #666;
        margin: 0;
      }
    }
  }
}

// Responsive design
@media (max-width: 768px) {
  .dashboard-container {
    padding: 10px;
  }
  
  .welcome-section {
    flex-direction: column;
    text-align: center;
    gap: 20px;
    padding: 30px 20px;
    
    .welcome-content h1 {
      font-size: 24px;
    }
    
    .quick-actions button {
      width: 100%;
    }
  }
  
  .stats-section {
    .stat-card {
      margin-bottom: 16px;
    }
  }
  
  .bookings-card .card-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 12px;
    
    h2 {
      font-size: 20px;
    }
  }
  
  // Hide some table columns on mobile
  ::ng-deep .ant-table {
    .ant-table-thead > tr > th:nth-child(3),
    .ant-table-tbody > tr > td:nth-child(3) {
      display: none;
    }
  }
}

@media (max-width: 576px) {
  .welcome-section {
    padding: 20px 15px;
    
    .welcome-content h1 {
      font-size: 20px;
    }
  }
  
  .quick-link-card .quick-link-content {
    padding: 15px;
    
    .quick-link-icon {
      font-size: 36px;
    }
    
    h3 {
      font-size: 16px;
    }
  }
}
