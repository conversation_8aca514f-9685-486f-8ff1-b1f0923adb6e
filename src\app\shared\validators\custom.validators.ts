import { AbstractControl, ValidationErrors, ValidatorFn } from '@angular/forms';

export class CustomValidators {
  static phoneNumber(control: AbstractControl): ValidationErrors | null {
    if (!control.value) return null;
    
    const phoneRegex = /^(\+84|0)[3|5|7|8|9][0-9]{8}$/;
    return phoneRegex.test(control.value) ? null : { phoneNumber: true };
  }

  static idCardNumber(control: AbstractControl): ValidationErrors | null {
    if (!control.value) return null;
    
    const idCardRegex = /^[0-9]{9}$|^[0-9]{12}$/;
    return idCardRegex.test(control.value) ? null : { idCardNumber: true };
  }

  static licensePlate(control: AbstractControl): ValidationErrors | null {
    if (!control.value) return null;
    
    const licensePlateRegex = /^[0-9]{2}[A-Z]{1,2}-[0-9]{4,5}$/;
    return licensePlateRegex.test(control.value) ? null : { licensePlate: true };
  }

  static driverLicenseNumber(control: AbstractControl): ValidationErrors | null {
    if (!control.value) return null;
    
    const licenseRegex = /^[0-9]{12}$/;
    return licenseRegex.test(control.value) ? null : { driverLicenseNumber: true };
  }

  static dateRange(startDateField: string, endDateField: string): ValidatorFn {
    return (control: AbstractControl): ValidationErrors | null => {
      const startDate = control.get(startDateField)?.value;
      const endDate = control.get(endDateField)?.value;

      if (!startDate || !endDate) return null;

      const start = new Date(startDate);
      const end = new Date(endDate);

      return start < end ? null : { dateRange: true };
    };
  }

  static futureDate(control: AbstractControl): ValidationErrors | null {
    if (!control.value) return null;

    const inputDate = new Date(control.value);
    const today = new Date();
    today.setHours(0, 0, 0, 0);

    return inputDate >= today ? null : { futureDate: true };
  }

  static pastDate(control: AbstractControl): ValidationErrors | null {
    if (!control.value) return null;

    const inputDate = new Date(control.value);
    const today = new Date();
    today.setHours(23, 59, 59, 999);

    return inputDate <= today ? null : { pastDate: true };
  }

  static positiveNumber(control: AbstractControl): ValidationErrors | null {
    if (!control.value) return null;

    const value = parseFloat(control.value);
    return value > 0 ? null : { positiveNumber: true };
  }

  static contractNumber(control: AbstractControl): ValidationErrors | null {
    if (!control.value) return null;
    
    const contractRegex = /^CR[0-9]{6}$/;
    return contractRegex.test(control.value) ? null : { contractNumber: true };
  }
}
