export interface Payment {
  id: number;
  contractId: number;
  paymentDate: Date;
  amount: number;
  paymentMethod: PaymentMethod;
  status: PaymentStatus;
  note?: string;
  createdAt: Date;
  updatedAt: Date;
  contract?: {
    id: number;
    contractNumber: string;
    customer: {
      fullName: string;
    };
  };
}

export enum PaymentMethod {
  CASH = 'CASH',
  CREDIT_CARD = 'CREDIT_CARD',
  DEBIT_CARD = 'DEBIT_CARD',
  BANK_TRANSFER = 'BANK_TRANSFER',
  DIGITAL_WALLET = 'DIGITAL_WALLET'
}

export enum PaymentStatus {
  PENDING = 'PENDING',
  COMPLETED = 'COMPLETED',
  FAILED = 'FAILED',
  REFUNDED = 'REFUNDED'
}

export interface CreatePaymentRequest {
  contractId: number;
  amount: number;
  paymentMethod: PaymentMethod;
  note?: string;
}

export interface UpdatePaymentRequest {
  id: number;
  contractId: number;
  amount: number;
  paymentMethod: PaymentMethod;
  status: PaymentStatus;
  note?: string;
}

import { BaseFilter } from './auth.model';

export interface PaymentFilter extends BaseFilter {
  contractId?: number;
  paymentMethod?: PaymentMethod;
  status?: PaymentStatus;
  startDate?: Date;
  endDate?: Date;
  minAmount?: number;
  maxAmount?: number;
}
