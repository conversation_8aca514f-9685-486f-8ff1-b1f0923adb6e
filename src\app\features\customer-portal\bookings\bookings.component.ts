import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { RouterModule, ActivatedRoute } from '@angular/router';
import { ReactiveFormsModule, FormBuilder, FormGroup } from '@angular/forms';
import { NzCardModule } from 'ng-zorro-antd/card';
import { NzButtonModule } from 'ng-zorro-antd/button';
import { NzTableModule } from 'ng-zorro-antd/table';
import { NzTagModule } from 'ng-zorro-antd/tag';
import { NzFormModule } from 'ng-zorro-antd/form';
import { NzSelectModule } from 'ng-zorro-antd/select';
import { NzDatePickerModule } from 'ng-zorro-antd/date-picker';
import { NzIconModule } from 'ng-zorro-antd/icon';
import { NzEmptyModule } from 'ng-zorro-antd/empty';
import { NzModalModule } from 'ng-zorro-antd/modal';
import { NzMessageService } from 'ng-zorro-antd/message';
import { Observable, BehaviorSubject } from 'rxjs';
import { debounceTime, distinctUntilChanged, switchMap } from 'rxjs/operators';

import { CustomerAuthService } from '../../../core/services/customer-auth.service';
import { ContractService } from '../../../core/services';
import { Contract, ContractFilter, ContractStatus, User } from '../../../core/models';
import { StatusColorPipe } from '../../../shared/pipes/status-color.pipe';
import { CurrencyFormatPipe } from '../../../shared/pipes/currency-format.pipe';
import { ConfirmationModalComponent } from '../../../shared/components/confirmation-modal/confirmation-modal.component';

@Component({
  selector: 'app-customer-bookings',
  standalone: true,
  imports: [
    CommonModule,
    RouterModule,
    ReactiveFormsModule,
    NzCardModule,
    NzButtonModule,
    NzTableModule,
    NzTagModule,
    NzFormModule,
    NzSelectModule,
    NzDatePickerModule,
    NzIconModule,
    NzEmptyModule,
    NzModalModule,
    StatusColorPipe,
    CurrencyFormatPipe,
    ConfirmationModalComponent
  ],
  templateUrl: './bookings.component.html',
  styleUrls: ['./bookings.component.scss']
})
export class CustomerBookingsComponent implements OnInit {
  currentUser: User | null = null;
  bookings: Contract[] = [];
  loading = false;
  total = 0;
  pageSize = 10;
  pageIndex = 1;
  
  filterForm!: FormGroup;
  private filterSubject = new BehaviorSubject<ContractFilter>({});
  
  cancelModalVisible = false;
  cancelLoading = false;
  bookingToCancel: Contract | null = null;
  
  contractStatusOptions = Object.values(ContractStatus);

  constructor(
    private fb: FormBuilder,
    private route: ActivatedRoute,
    private customerAuthService: CustomerAuthService,
    private contractService: ContractService,
    private message: NzMessageService
  ) {}

  ngOnInit(): void {
    this.currentUser = this.customerAuthService.getCurrentCustomer();
    this.initFilterForm();
    this.setupFilterSubscription();
    this.loadBookings();
    this.checkNewBooking();
  }

  private initFilterForm(): void {
    this.filterForm = this.fb.group({
      status: [null],
      startDate: [null],
      endDate: [null]
    });
  }

  private setupFilterSubscription(): void {
    this.filterForm.valueChanges.pipe(
      debounceTime(300),
      distinctUntilChanged()
    ).subscribe(values => {
      const filter: ContractFilter = {
        ...values,
        customerId: this.currentUser?.id,
        page: 1,
        limit: this.pageSize
      };
      this.filterSubject.next(filter);
      this.pageIndex = 1;
    });

    this.filterSubject.pipe(
      switchMap(filter => {
        this.loading = true;
        return this.contractService.getContracts(filter);
      })
    ).subscribe({
      next: (response) => {
        this.bookings = response.data;
        this.total = response.total;
        this.loading = false;
      },
      error: (error) => {
        this.message.error('Failed to load bookings');
        this.loading = false;
      }
    });
  }

  private loadBookings(): void {
    const filter: ContractFilter = {
      ...this.filterForm.value,
      customerId: this.currentUser?.id,
      page: this.pageIndex,
      limit: this.pageSize
    };
    this.filterSubject.next(filter);
  }

  private checkNewBooking(): void {
    this.route.queryParams.subscribe(params => {
      if (params['newBooking']) {
        this.message.success('Your booking has been confirmed successfully!');
      }
    });
  }

  onPageChange(pageIndex: number): void {
    this.pageIndex = pageIndex;
    this.loadBookings();
  }

  onPageSizeChange(pageSize: number): void {
    this.pageSize = pageSize;
    this.pageIndex = 1;
    this.loadBookings();
  }

  resetFilters(): void {
    this.filterForm.reset();
  }

  getBookingStatusText(status: string): string {
    const statusMap: { [key: string]: string } = {
      'PENDING': 'Pending Approval',
      'CONFIRMED': 'Confirmed',
      'ACTIVE': 'Active Rental',
      'COMPLETED': 'Completed',
      'CANCELLED': 'Cancelled'
    };
    return statusMap[status] || status;
  }

  getRentalDuration(contract: Contract): number {
    const start = new Date(contract.startDate);
    const end = new Date(contract.endDate);
    return Math.ceil((end.getTime() - start.getTime()) / (1000 * 3600 * 24));
  }

  canCancelBooking(contract: Contract): boolean {
    const now = new Date();
    const startDate = new Date(contract.startDate);
    const hoursUntilStart = (startDate.getTime() - now.getTime()) / (1000 * 3600);
    
    return contract.status === 'PENDING' || 
           (contract.status === 'CONFIRMED' && hoursUntilStart > 24);
  }

  showCancelModal(booking: Contract): void {
    this.bookingToCancel = booking;
    this.cancelModalVisible = true;
  }

  confirmCancel(): void {
    if (this.bookingToCancel) {
      this.cancelLoading = true;
      
      // Update contract status to cancelled
      this.contractService.updateContract({
        id: this.bookingToCancel.id,
        status: ContractStatus.CANCELLED
      }).subscribe({
        next: () => {
          this.message.success('Booking cancelled successfully');
          this.cancelModalVisible = false;
          this.cancelLoading = false;
          this.bookingToCancel = null;
          this.loadBookings();
        },
        error: (error) => {
          this.message.error('Failed to cancel booking');
          this.cancelLoading = false;
        }
      });
    }
  }

  cancelCancel(): void {
    this.cancelModalVisible = false;
    this.bookingToCancel = null;
  }

  viewBookingDetails(booking: Contract): void {
    // Navigate to booking details page
    // This would be implemented when we create the routing
  }

  downloadReceipt(booking: Contract): void {
    // Generate and download receipt
    this.message.info('Receipt download feature will be implemented');
  }

  getStatusIcon(status: string): string {
    const iconMap: { [key: string]: string } = {
      'PENDING': 'clock-circle',
      'CONFIRMED': 'check-circle',
      'ACTIVE': 'play-circle',
      'COMPLETED': 'check-circle',
      'CANCELLED': 'close-circle'
    };
    return iconMap[status] || 'clock-circle';
  }
}
