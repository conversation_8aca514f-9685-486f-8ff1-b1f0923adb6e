<app-page-header 
  [title]="isEditMode ? 'Edit Vehicle' : 'Add Vehicle'" 
  [subtitle]="isEditMode ? 'Update vehicle information' : 'Create a new vehicle'"
  [showBackButton]="true">
</app-page-header>

<nz-card [nzLoading]="loading">
  <form nz-form [formGroup]="vehicleForm" (ngSubmit)="onSubmit()">
    <div nz-row [nzGutter]="[16, 16]">
      <div nz-col nzXs="24" nzSm="12">
        <nz-form-item>
          <nz-form-label [nzSpan]="24" nzRequired>License Plate</nz-form-label>
          <nz-form-control [nzSpan]="24" nzErrorTip="Please enter license plate">
            <input nz-input formControlName="licensePlate" placeholder="e.g., 30A-12345">
          </nz-form-control>
        </nz-form-item>
      </div>

      <div nz-col nzXs="24" nzSm="12">
        <nz-form-item>
          <nz-form-label [nzSpan]="24" nzRequired>Vehicle Type</nz-form-label>
          <nz-form-control [nzSpan]="24" nzErrorTip="Please select vehicle type">
            <nz-select formControlName="vehicleTypeId" nzPlaceHolder="Select vehicle type">
              <nz-option *ngFor="let type of vehicleTypes" [nzValue]="type.id" [nzLabel]="type.nameVehicleType"></nz-option>
            </nz-select>
          </nz-form-control>
        </nz-form-item>
      </div>

      <div nz-col nzXs="24" nzSm="12">
        <nz-form-item>
          <nz-form-label [nzSpan]="24" nzRequired>Seat Count</nz-form-label>
          <nz-form-control [nzSpan]="24" nzErrorTip="Please enter seat count">
            <nz-select formControlName="seatCount" nzPlaceHolder="Select seat count">
              <nz-option nzValue="4" nzLabel="4 seats"></nz-option>
              <nz-option nzValue="5" nzLabel="5 seats"></nz-option>
              <nz-option nzValue="7" nzLabel="7 seats"></nz-option>
              <nz-option nzValue="16" nzLabel="16 seats"></nz-option>
            </nz-select>
          </nz-form-control>
        </nz-form-item>
      </div>

      <div nz-col nzXs="24" nzSm="12">
        <nz-form-item>
          <nz-form-label [nzSpan]="24" nzRequired>Color</nz-form-label>
          <nz-form-control [nzSpan]="24" nzErrorTip="Please enter color">
            <input nz-input formControlName="color" placeholder="e.g., White, Black, Red">
          </nz-form-control>
        </nz-form-item>
      </div>

      <div nz-col nzXs="24" nzSm="12">
        <nz-form-item>
          <nz-form-label [nzSpan]="24" nzRequired>Base Price (VND)</nz-form-label>
          <nz-form-control [nzSpan]="24" nzErrorTip="Please enter base price">
            <nz-input-number 
              formControlName="basePrice" 
              [nzMin]="0" 
              [nzStep]="100000"
              nzPlaceHolder="Enter base price"
              style="width: 100%">
            </nz-input-number>
          </nz-form-control>
        </nz-form-item>
      </div>

      <div nz-col nzXs="24" nzSm="12" *ngIf="isEditMode">
        <nz-form-item>
          <nz-form-label [nzSpan]="24">Status</nz-form-label>
          <nz-form-control [nzSpan]="24">
            <nz-select formControlName="status" nzPlaceHolder="Select status">
              <nz-option *ngFor="let status of vehicleStatusOptions" [nzValue]="status" [nzLabel]="status"></nz-option>
            </nz-select>
          </nz-form-control>
        </nz-form-item>
      </div>

      <div nz-col nzSpan="24">
        <nz-form-item>
          <nz-form-label [nzSpan]="24">Image URL</nz-form-label>
          <nz-form-control [nzSpan]="24">
            <input nz-input formControlName="imageUrl" placeholder="Enter image URL (optional)">
          </nz-form-control>
        </nz-form-item>
      </div>
    </div>

    <div nz-row>
      <div nz-col nzSpan="24" class="form-actions">
        <button nz-button nzType="default" (click)="onCancel()" [disabled]="submitting">
          Cancel
        </button>
        <button 
          nz-button 
          nzType="primary" 
          [nzLoading]="submitting"
          [disabled]="!vehicleForm.valid"
          style="margin-left: 8px;">
          {{ isEditMode ? 'Update' : 'Create' }}
        </button>
      </div>
    </div>
  </form>
</nz-card>
