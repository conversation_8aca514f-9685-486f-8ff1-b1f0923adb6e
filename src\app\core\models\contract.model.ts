export interface Contract {
  id: number;
  contractNumber: string;
  customerId: number;
  vehicleId: number;
  driverId?: number;
  rentalType: RentalType;
  startDate: Date;
  endDate: Date;
  totalPrice: number;
  depositAmount: number;
  status: ContractStatus;
  createdAt: Date;
  updatedAt: Date;
  customer?: {
    id: number;
    fullName: string;
    email: string;
    phone: string;
  };
  vehicle?: {
    id: number;
    licensePlate: string;
    vehicleType: {
      nameVehicleType: string;
    };
  };
  driver?: {
    id: number;
    fullName: string;
    phone: string;
  };
}

export enum RentalType {
  SELF_DRIVE = 'SELF_DRIVE',
  WITH_DRIVER = 'WITH_DRIVER'
}

export enum ContractStatus {
  PENDING = 'PENDING',
  CONFIRMED = 'CONFIRMED',
  ACTIVE = 'ACTIVE',
  COMPLETED = 'COMPLETED',
  CANCELLED = 'CANCELLED'
}

export interface CreateContractRequest {
  contractNumber: string;
  customerId: number;
  vehicleId: number;
  driverId?: number;
  rentalType: RentalType;
  startDate: Date;
  endDate: Date;
  totalPrice: number;
  depositAmount: number;
}

export interface UpdateContractRequest {
  id: number;
  customerId?: number;
  vehicleId?: number;
  driverId?: number;
  rentalType?: RentalType;
  startDate?: Date;
  endDate?: Date;
  totalPrice?: number;
  depositAmount?: number;
  status: ContractStatus;
}

import { BaseFilter } from './auth.model';

export interface ContractFilter extends BaseFilter {
  customerId?: number;
  vehicleId?: number;
  driverId?: number;
  rentalType?: RentalType;
  status?: ContractStatus;
  startDate?: Date;
  endDate?: Date;
  search?: string;
}
