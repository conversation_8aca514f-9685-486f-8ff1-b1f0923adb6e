import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { NzTableModule } from 'ng-zorro-antd/table';
import { NzButtonModule } from 'ng-zorro-antd/button';
import { NzCardModule } from 'ng-zorro-antd/card';
import { NzMessageService } from 'ng-zorro-antd/message';
import { PageHeaderComponent } from '../../../shared/components/page-header/page-header.component';
import { VehicleTypeService } from '../../../core/services';
import { VehicleType } from '../../../core/models';

@Component({
  selector: 'app-vehicle-type-list',
  standalone: true,
  imports: [
    CommonModule,
    NzTableModule,
    NzButtonModule,
    NzCardModule,
    PageHeaderComponent
  ],
  templateUrl: './vehicle-type-list.component.html',
  styleUrls: ['./vehicle-type-list.component.scss']
})
export class VehicleTypeListComponent implements OnInit {
  vehicleTypes: VehicleType[] = [];
  loading = false;

  constructor(
    private vehicleTypeService: VehicleTypeService,
    private message: NzMessageService
  ) {}

  ngOnInit(): void {
    this.loadVehicleTypes();
  }

  private loadVehicleTypes(): void {
    this.loading = true;
    this.vehicleTypeService.getAllVehicleTypes().subscribe({
      next: (types) => {
        this.vehicleTypes = types;
        this.loading = false;
      },
      error: (error) => {
        this.message.error('Failed to load vehicle types');
        this.loading = false;
      }
    });
  }
}
