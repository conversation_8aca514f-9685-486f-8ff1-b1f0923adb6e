<app-page-header 
  [title]="isEditMode ? 'Edit Driver' : 'Add Driver'" 
  [subtitle]="isEditMode ? 'Update driver information' : 'Create a new driver'"
  [showBackButton]="true">
</app-page-header>

<nz-card [nzLoading]="loading">
  <form nz-form [formGroup]="driverForm" (ngSubmit)="onSubmit()">
    <div nz-row [nzGutter]="[16, 16]">
      <div nz-col nzXs="24" nzSm="12">
        <nz-form-item>
          <nz-form-label [nzSpan]="24" nzRequired>Full Name</nz-form-label>
          <nz-form-control [nzSpan]="24" nzErrorTip="Please enter full name (min 2 characters)">
            <input nz-input formControlName="fullName" placeholder="Enter full name">
          </nz-form-control>
        </nz-form-item>
      </div>

      <div nz-col nzXs="24" nzSm="12">
        <nz-form-item>
          <nz-form-label [nzSpan]="24" nzRequired>Email</nz-form-label>
          <nz-form-control [nzSpan]="24" nzErrorTip="Please enter a valid email address">
            <input nz-input formControlName="email" placeholder="Enter email address" type="email">
          </nz-form-control>
        </nz-form-item>
      </div>

      <div nz-col nzXs="24" nzSm="12">
        <nz-form-item>
          <nz-form-label [nzSpan]="24" nzRequired>Phone Number</nz-form-label>
          <nz-form-control [nzSpan]="24" nzErrorTip="Please enter a valid Vietnamese phone number">
            <input nz-input formControlName="phone" placeholder="e.g., 0901234567">
          </nz-form-control>
        </nz-form-item>
      </div>

      <div nz-col nzXs="24" nzSm="12">
        <nz-form-item>
          <nz-form-label [nzSpan]="24" nzRequired>ID Card Number</nz-form-label>
          <nz-form-control [nzSpan]="24" nzErrorTip="Please enter a valid ID card number (9 or 12 digits)">
            <input nz-input formControlName="idCardNumber" placeholder="e.g., 123456789">
          </nz-form-control>
        </nz-form-item>
      </div>

      <div nz-col nzSpan="24">
        <nz-form-item>
          <nz-form-label [nzSpan]="24" nzRequired>Address</nz-form-label>
          <nz-form-control [nzSpan]="24" nzErrorTip="Please enter address">
            <textarea nz-input formControlName="address" placeholder="Enter full address" rows="3"></textarea>
          </nz-form-control>
        </nz-form-item>
      </div>

      <div nz-col nzXs="24" nzSm="12">
        <nz-form-item>
          <nz-form-label [nzSpan]="24" nzRequired>Driver License Number</nz-form-label>
          <nz-form-control [nzSpan]="24" nzErrorTip="Please enter a valid license number (12 digits)">
            <input nz-input formControlName="driverLicenseNumber" placeholder="e.g., 123456789012">
          </nz-form-control>
        </nz-form-item>
      </div>

      <div nz-col nzXs="24" nzSm="12" *ngIf="isEditMode">
        <nz-form-item>
          <nz-form-label [nzSpan]="24">Status</nz-form-label>
          <nz-form-control [nzSpan]="24">
            <nz-select formControlName="status" nzPlaceHolder="Select status">
              <nz-option *ngFor="let status of driverStatusOptions" [nzValue]="status" [nzLabel]="status"></nz-option>
            </nz-select>
          </nz-form-control>
        </nz-form-item>
      </div>

      <div nz-col nzXs="24" nzSm="12">
        <nz-form-item>
          <nz-form-label [nzSpan]="24" nzRequired>License Issue Date</nz-form-label>
          <nz-form-control [nzSpan]="24" nzErrorTip="Please select issue date">
            <nz-date-picker 
              formControlName="licenseIssueDate" 
              [nzDisabledDate]="disabledDate"
              nzPlaceHolder="Select issue date"
              style="width: 100%">
            </nz-date-picker>
          </nz-form-control>
        </nz-form-item>
      </div>

      <div nz-col nzXs="24" nzSm="12">
        <nz-form-item>
          <nz-form-label [nzSpan]="24" nzRequired>License Expiry Date</nz-form-label>
          <nz-form-control [nzSpan]="24" nzErrorTip="Please select expiry date">
            <nz-date-picker 
              formControlName="licenseExpiryDate" 
              [nzDisabledDate]="disabledExpiryDate"
              nzPlaceHolder="Select expiry date"
              style="width: 100%">
            </nz-date-picker>
          </nz-form-control>
        </nz-form-item>
      </div>
    </div>

    <div nz-row>
      <div nz-col nzSpan="24" class="form-actions">
        <button nz-button nzType="default" (click)="onCancel()" [disabled]="submitting">
          Cancel
        </button>
        <button 
          nz-button 
          nzType="primary" 
          [nzLoading]="submitting"
          [disabled]="!driverForm.valid"
          style="margin-left: 8px;">
          {{ isEditMode ? 'Update' : 'Create' }}
        </button>
      </div>
    </div>
  </form>
</nz-card>
