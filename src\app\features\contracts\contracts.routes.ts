import { Routes } from '@angular/router';

export const contractRoutes: Routes = [
  {
    path: '',
    redirectTo: 'list',
    pathMatch: 'full'
  },
  {
    path: 'list',
    loadComponent: () => import('./contract-list/contract-list.component').then(m => m.ContractListComponent)
  },
  {
    path: 'create',
    loadComponent: () => import('./contract-form/contract-form.component').then(m => m.ContractFormComponent)
  },
  {
    path: 'edit/:id',
    loadComponent: () => import('./contract-form/contract-form.component').then(m => m.ContractFormComponent)
  }
];
