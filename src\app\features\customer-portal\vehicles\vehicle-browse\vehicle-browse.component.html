<div class="vehicle-browse-container">
  <!-- Hero Section with Search -->
  <div class="hero-section">
    <div class="hero-content">
      <h1>Find Your Perfect Rental Car</h1>
      <p>Choose from our wide selection of quality vehicles for your journey</p>
      
      <nz-card class="search-card">
        <form nz-form [formGroup]="searchForm" class="search-form">
          <div nz-row [nzGutter]="[16, 16]">
            <div nz-col nzXs="24" nzSm="12" nzMd="6">
              <nz-form-item>
                <nz-form-label>Pick-up Date</nz-form-label>
                <nz-form-control>
                  <nz-date-picker 
                    formControlName="startDate" 
                    [nzDisabledDate]="disabledDate"
                    nzPlaceHolder="Select date"
                    style="width: 100%">
                  </nz-date-picker>
                </nz-form-control>
              </nz-form-item>
            </div>
            
            <div nz-col nzXs="24" nzSm="12" nzMd="6">
              <nz-form-item>
                <nz-form-label>Return Date</nz-form-label>
                <nz-form-control>
                  <nz-date-picker 
                    formControlName="endDate" 
                    [nzDisabledDate]="disabledEndDate"
                    nzPlaceHolder="Select date"
                    style="width: 100%">
                  </nz-date-picker>
                </nz-form-control>
              </nz-form-item>
            </div>
            
            <div nz-col nzXs="24" nzSm="12" nzMd="6">
              <nz-form-item>
                <nz-form-label>Vehicle Type</nz-form-label>
                <nz-form-control>
                  <nz-select formControlName="vehicleTypeId" nzPlaceHolder="Any type" nzAllowClear>
                    <nz-option *ngFor="let type of vehicleTypes" [nzValue]="type.id" [nzLabel]="type.nameVehicleType"></nz-option>
                  </nz-select>
                </nz-form-control>
              </nz-form-item>
            </div>
            
            <div nz-col nzXs="24" nzSm="12" nzMd="6">
              <nz-form-item>
                <nz-form-label>Seats</nz-form-label>
                <nz-form-control>
                  <nz-select formControlName="seatCount" nzPlaceHolder="Any seats" nzAllowClear>
                    <nz-option nzValue="4" nzLabel="4 seats"></nz-option>
                    <nz-option nzValue="5" nzLabel="5 seats"></nz-option>
                    <nz-option nzValue="7" nzLabel="7 seats"></nz-option>
                    <nz-option nzValue="16" nzLabel="16 seats"></nz-option>
                  </nz-select>
                </nz-form-control>
              </nz-form-item>
            </div>
          </div>
          
          <!-- Price Range -->
          <div nz-row [nzGutter]="[16, 16]" class="price-filters">
            <div nz-col nzXs="24" nzSm="12" nzMd="8">
              <nz-form-item>
                <nz-form-label>Min Price (VND/day)</nz-form-label>
                <nz-form-control>
                  <nz-input-number 
                    formControlName="minPrice" 
                    [nzMin]="0" 
                    [nzStep]="100000"
                    nzPlaceHolder="Min price"
                    style="width: 100%">
                  </nz-input-number>
                </nz-form-control>
              </nz-form-item>
            </div>
            
            <div nz-col nzXs="24" nzSm="12" nzMd="8">
              <nz-form-item>
                <nz-form-label>Max Price (VND/day)</nz-form-label>
                <nz-form-control>
                  <nz-input-number 
                    formControlName="maxPrice" 
                    [nzMin]="0" 
                    [nzStep]="100000"
                    nzPlaceHolder="Max price"
                    style="width: 100%">
                  </nz-input-number>
                </nz-form-control>
              </nz-form-item>
            </div>
            
            <div nz-col nzXs="24" nzSm="24" nzMd="8">
              <nz-form-item>
                <nz-form-label>&nbsp;</nz-form-label>
                <nz-form-control>
                  <button nz-button nzType="default" (click)="resetFilters()" style="width: 100%;">
                    <span nz-icon nzType="reload"></span>
                    Reset Filters
                  </button>
                </nz-form-control>
              </nz-form-item>
            </div>
          </div>
        </form>
      </nz-card>
    </div>
  </div>

  <!-- Results Section -->
  <div class="results-section">
    <div class="results-header">
      <h2>Available Vehicles</h2>
      <div class="results-info" *ngIf="!loading">
        <span>{{ total }} vehicles found</span>
        <span *ngIf="calculateRentalDays() > 1"> • {{ calculateRentalDays() }} days rental</span>
      </div>
    </div>

    <!-- Loading State -->
    <div *ngIf="loading" class="loading-container">
      <nz-spin nzSize="large" nzTip="Searching for available vehicles..."></nz-spin>
    </div>

    <!-- Vehicle Grid -->
    <div *ngIf="!loading && vehicles.length > 0" class="vehicle-grid">
      <div nz-row [nzGutter]="[24, 24]">
        <div nz-col nzXs="24" nzSm="12" nzMd="8" nzLg="6" *ngFor="let vehicle of vehicles">
          <nz-card class="vehicle-card" [nzCover]="coverTemplate" [nzActions]="[actionView]">
            <ng-template #coverTemplate>
              <div class="vehicle-image">
                <img
                  [src]="vehicle.imageUrl || '/assets/images/default-car.jpg'"
                  [alt]="vehicle.vehicleType?.nameVehicleType || 'Vehicle'"
                  (error)="$any($event.target).src='/assets/images/default-car.jpg'">
                <div class="vehicle-status">
                  <nz-tag nzColor="green">Available</nz-tag>
                </div>
              </div>
            </ng-template>
            
            <nz-card-meta
              [nzTitle]="vehicle.vehicleType?.nameVehicleType || 'Vehicle'"
              [nzDescription]="vehicle.licensePlate">
            </nz-card-meta>
            
            <div class="vehicle-details">
              <div class="detail-item">
                <span nz-icon nzType="user"></span>
                <span>{{ vehicle.seatCount }} seats</span>
              </div>
              <div class="detail-item">
                <span nz-icon nzType="bg-colors"></span>
                <span>{{ vehicle.color }}</span>
              </div>
            </div>
            
            <div class="pricing">
              <div class="daily-price">
                <span class="price">{{ vehicle.basePrice | currencyFormat }}</span>
                <span class="period">/day</span>
              </div>
              <div class="total-price" *ngIf="calculateRentalDays() > 1">
                <span class="label">Total: </span>
                <span class="price">{{ calculateTotalPrice(vehicle) | currencyFormat }}</span>
              </div>
            </div>
            
            <ng-template #actionView>
              <span nz-icon nzType="eye" (click)="viewVehicleDetails(vehicle)"></span>
            </ng-template>
          </nz-card>
        </div>
      </div>
    </div>

    <!-- Empty State -->
    <div *ngIf="!loading && vehicles.length === 0" class="empty-state">
      <nz-empty 
        nzNotFoundImage="simple" 
        nzNotFoundContent="No vehicles found matching your criteria">
        <ng-container nzNotFoundFooter>
          <button nz-button nzType="primary" (click)="resetFilters()">
            Reset Filters
          </button>
        </ng-container>
      </nz-empty>
    </div>

    <!-- Pagination -->
    <div *ngIf="!loading && vehicles.length > 0 && total > pageSize" class="pagination-container">
      <nz-pagination 
        [nzPageIndex]="pageIndex"
        [nzTotal]="total"
        [nzPageSize]="pageSize"
        [nzShowSizeChanger]="true"
        [nzShowQuickJumper]="true"
        (nzPageIndexChange)="onPageChange($event)">
      </nz-pagination>
    </div>
  </div>
</div>
