.vehicle-browse-container {
  min-height: 100vh;
  background: #f5f5f5;
}

.hero-section {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  padding: 60px 20px;
  color: white;
  text-align: center;
}

.hero-content {
  max-width: 1200px;
  margin: 0 auto;
  
  h1 {
    font-size: 48px;
    font-weight: 700;
    margin-bottom: 16px;
    color: white;
  }
  
  p {
    font-size: 20px;
    margin-bottom: 40px;
    opacity: 0.9;
  }
}

.search-card {
  background: white;
  border-radius: 12px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  
  ::ng-deep .ant-card-body {
    padding: 32px;
  }
}

.search-form {
  .price-filters {
    margin-top: 16px;
    padding-top: 16px;
    border-top: 1px solid #f0f0f0;
  }
}

.results-section {
  max-width: 1200px;
  margin: 0 auto;
  padding: 40px 20px;
}

.results-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 32px;
  
  h2 {
    font-size: 32px;
    font-weight: 600;
    color: #333;
    margin: 0;
  }
  
  .results-info {
    color: #666;
    font-size: 16px;
  }
}

.loading-container {
  text-align: center;
  padding: 80px 0;
}

.vehicle-grid {
  margin-bottom: 40px;
}

.vehicle-card {
  height: 100%;
  border-radius: 12px;
  overflow: hidden;
  transition: all 0.3s ease;
  
  &:hover {
    transform: translateY(-4px);
    box-shadow: 0 12px 32px rgba(0, 0, 0, 0.15);
  }
  
  ::ng-deep .ant-card-body {
    padding: 16px;
  }
  
  ::ng-deep .ant-card-actions {
    background: #f8f9fa;
    border-top: 1px solid #f0f0f0;
    
    li {
      margin: 8px 0;
      
      span {
        color: #1890ff;
        font-size: 18px;
        cursor: pointer;
        
        &:hover {
          color: #40a9ff;
        }
      }
    }
  }
}

.vehicle-image {
  position: relative;
  height: 200px;
  overflow: hidden;
  
  img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform 0.3s ease;
  }
  
  .vehicle-status {
    position: absolute;
    top: 12px;
    right: 12px;
  }
}

.vehicle-card:hover .vehicle-image img {
  transform: scale(1.05);
}

.vehicle-details {
  margin: 16px 0;
  
  .detail-item {
    display: flex;
    align-items: center;
    margin-bottom: 8px;
    color: #666;
    
    span:first-child {
      margin-right: 8px;
      color: #1890ff;
    }
  }
}

.pricing {
  border-top: 1px solid #f0f0f0;
  padding-top: 16px;
  
  .daily-price {
    display: flex;
    align-items: baseline;
    margin-bottom: 8px;
    
    .price {
      font-size: 24px;
      font-weight: 700;
      color: #1890ff;
    }
    
    .period {
      font-size: 14px;
      color: #666;
      margin-left: 4px;
    }
  }
  
  .total-price {
    display: flex;
    align-items: center;
    
    .label {
      font-size: 14px;
      color: #666;
    }
    
    .price {
      font-size: 18px;
      font-weight: 600;
      color: #52c41a;
      margin-left: 4px;
    }
  }
}

.empty-state {
  text-align: center;
  padding: 80px 0;
}

.pagination-container {
  text-align: center;
  padding: 40px 0;
}

// Responsive design
@media (max-width: 768px) {
  .hero-content {
    h1 {
      font-size: 32px;
    }
    
    p {
      font-size: 16px;
    }
  }
  
  .search-card {
    ::ng-deep .ant-card-body {
      padding: 20px;
    }
  }
  
  .results-section {
    padding: 20px 10px;
  }
  
  .results-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 8px;
    
    h2 {
      font-size: 24px;
    }
  }
  
  .vehicle-image {
    height: 160px;
  }
}

@media (max-width: 576px) {
  .hero-section {
    padding: 40px 15px;
  }
  
  .hero-content h1 {
    font-size: 28px;
  }
  
  .vehicle-card {
    margin-bottom: 16px;
  }
}
