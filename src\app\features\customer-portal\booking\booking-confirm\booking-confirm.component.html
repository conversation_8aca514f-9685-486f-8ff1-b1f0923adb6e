<div class="booking-confirm-container" *ngIf="bookingData && vehicle">
  <!-- Header -->
  <div class="page-header">
    <button nz-button nzType="link" (click)="goBack()" class="back-button">
      <span nz-icon nzType="arrow-left"></span>
      Back to Vehicle Details
    </button>
    <h1>Confirm Your Booking</h1>
  </div>

  <!-- Progress Steps -->
  <nz-card class="steps-card">
    <nz-steps [nzCurrent]="currentStep">
      <nz-step nzTitle="Review Details" nzDescription="Check your booking information"></nz-step>
      <nz-step nzTitle="Payment Info" nzDescription="Select payment method"></nz-step>
      <nz-step nzTitle="Confirmation" nzDescription="Complete your booking"></nz-step>
    </nz-steps>
  </nz-card>

  <div nz-row [nzGutter]="[24, 24]">
    <!-- Booking Details -->
    <div nz-col nzXs="24" nzLg="14">
      <!-- Step 1: Review Details -->
      <nz-card nzTitle="Booking Details" class="details-card" *ngIf="currentStep === 0">
        <nz-descriptions nzBordered [nzColumn]="1">
          <nz-descriptions-item nzTitle="Vehicle">
            <div class="vehicle-info">
              <img 
                [src]="vehicle.imageUrl || '/assets/images/default-car.jpg'" 
                [alt]="vehicle.vehicleType?.nameVehicleType"
                class="vehicle-thumbnail">
              <div class="vehicle-details">
                <div class="vehicle-name">{{ vehicle.vehicleType?.nameVehicleType }}</div>
                <div class="vehicle-plate">{{ vehicle.licensePlate }}</div>
                <div class="vehicle-specs">{{ vehicle.seatCount }} seats • {{ vehicle.color }}</div>
              </div>
            </div>
          </nz-descriptions-item>
          <nz-descriptions-item nzTitle="Rental Period">
            {{ bookingData.startDate | date:'dd/MM/yyyy' }} - {{ bookingData.endDate | date:'dd/MM/yyyy' }}
            <span class="duration">({{ calculateRentalDays() }} days)</span>
          </nz-descriptions-item>
          <nz-descriptions-item nzTitle="Rental Type">
            <span *ngIf="bookingData.rentalType === 'SELF_DRIVE'">Self Drive</span>
            <span *ngIf="bookingData.rentalType === 'WITH_DRIVER'">With Driver</span>
          </nz-descriptions-item>
        </nz-descriptions>
        
        <div class="step-actions">
          <button nz-button nzType="primary" (click)="nextStep()">
            Continue to Payment
          </button>
        </div>
      </nz-card>

      <!-- Step 2: Payment Information -->
      <nz-card nzTitle="Payment Information" class="payment-card" *ngIf="currentStep === 1">
        <form nz-form [formGroup]="confirmationForm">
          <nz-form-item>
            <nz-form-label [nzSpan]="24" nzRequired>Payment Method</nz-form-label>
            <nz-form-control [nzSpan]="24" nzErrorTip="Please select a payment method">
              <nz-select formControlName="paymentMethod" nzPlaceHolder="Select payment method">
                <nz-option *ngFor="let method of paymentMethodOptions" [nzValue]="method" [nzLabel]="method"></nz-option>
              </nz-select>
            </nz-form-control>
          </nz-form-item>

          <nz-form-item>
            <nz-form-label [nzSpan]="24">Special Requests</nz-form-label>
            <nz-form-control [nzSpan]="24">
              <textarea 
                nz-input 
                formControlName="specialRequests" 
                placeholder="Any special requests or notes for your rental..."
                rows="3">
              </textarea>
            </nz-form-control>
          </nz-form-item>
        </form>
        
        <div class="step-actions">
          <button nz-button nzType="default" (click)="prevStep()">
            Back
          </button>
          <button nz-button nzType="primary" (click)="nextStep()" style="margin-left: 8px;">
            Review & Confirm
          </button>
        </div>
      </nz-card>

      <!-- Step 3: Final Confirmation -->
      <nz-card nzTitle="Final Confirmation" class="confirmation-card" *ngIf="currentStep === 2">
        <div class="confirmation-content">
          <div class="confirmation-message">
            <span nz-icon nzType="check-circle" class="success-icon"></span>
            <h3>Ready to Confirm Your Booking</h3>
            <p>Please review all details and confirm your booking. You will receive a confirmation email once the booking is processed.</p>
          </div>

          <form nz-form [formGroup]="confirmationForm">
            <nz-form-item>
              <nz-form-control [nzSpan]="24" nzErrorTip="Please agree to the terms and conditions">
                <label nz-checkbox formControlName="agreeToTerms">
                  I agree to the <a href="#" target="_blank">Terms and Conditions</a> and <a href="#" target="_blank">Rental Agreement</a>
                </label>
              </nz-form-control>
            </nz-form-item>
          </form>
        </div>
        
        <div class="step-actions">
          <button nz-button nzType="default" (click)="prevStep()">
            Back
          </button>
          <button 
            nz-button 
            nzType="primary" 
            [nzLoading]="submitting"
            [disabled]="!confirmationForm.valid"
            (click)="onConfirmBooking()"
            style="margin-left: 8px;">
            Confirm Booking
          </button>
        </div>
      </nz-card>
    </div>

    <!-- Booking Summary -->
    <div nz-col nzXs="24" nzLg="10">
      <div class="summary-panel">
        <nz-card nzTitle="Booking Summary" class="summary-card">
          <!-- Vehicle Summary -->
          <div class="summary-section">
            <h4>Vehicle</h4>
            <div class="summary-item">
              <span>{{ vehicle.vehicleType?.nameVehicleType }}</span>
              <span>{{ vehicle.licensePlate }}</span>
            </div>
          </div>

          <nz-divider></nz-divider>

          <!-- Rental Details -->
          <div class="summary-section">
            <h4>Rental Details</h4>
            <div class="summary-item">
              <span>Duration:</span>
              <span>{{ calculateRentalDays() }} day(s)</span>
            </div>
            <div class="summary-item">
              <span>Type:</span>
              <span>{{ bookingData.rentalType === 'SELF_DRIVE' ? 'Self Drive' : 'With Driver' }}</span>
            </div>
          </div>

          <nz-divider></nz-divider>

          <!-- Pricing Breakdown -->
          <div class="summary-section">
            <h4>Pricing</h4>
            <div class="summary-item">
              <span>Vehicle Cost:</span>
              <span>{{ calculateBasePrice() | currencyFormat }}</span>
            </div>
            <div class="summary-item" *ngIf="bookingData.rentalType === 'WITH_DRIVER'">
              <span>Driver Cost:</span>
              <span>{{ calculateDriverCost() | currencyFormat }}</span>
            </div>
            <div class="summary-item total">
              <span>Total Amount:</span>
              <span>{{ bookingData.totalPrice | currencyFormat }}</span>
            </div>
            <div class="summary-item deposit">
              <span>Deposit Required:</span>
              <span>{{ calculateDepositAmount() | currencyFormat }}</span>
            </div>
          </div>

          <nz-divider></nz-divider>

          <!-- Important Notes -->
          <div class="summary-section">
            <h4>Important Notes</h4>
            <ul class="notes-list">
              <li>Deposit is required to secure your booking</li>
              <li>Valid driver's license required for self-drive</li>
              <li>Vehicle inspection will be done before handover</li>
              <li>Free cancellation up to 24 hours before pickup</li>
            </ul>
          </div>
        </nz-card>
      </div>
    </div>
  </div>
</div>
