export interface ContractHistory {
  id: number;
  contractId: number;
  changeDate: Date;
  changeDescription: string;
  changeBy: string;
  contract?: {
    id: number;
    contractNumber: string;
  };
}

export interface CreateContractHistoryRequest {
  contractId: number;
  changeDescription: string;
  changeBy: string;
}

export interface ContractHistoryFilter {
  contractId?: number;
  startDate?: Date;
  endDate?: Date;
  changeBy?: string;
}
