.home-container {
  min-height: 100vh;
}

.hero-section {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  padding: 80px 20px;
  display: flex;
  align-items: center;
  min-height: 70vh;
  
  .hero-content {
    max-width: 1200px;
    margin: 0 auto;
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 60px;
    align-items: center;
    
    h1 {
      font-size: 48px;
      font-weight: 700;
      margin-bottom: 24px;
      color: white;
    }
    
    p {
      font-size: 20px;
      margin-bottom: 32px;
      opacity: 0.9;
      line-height: 1.6;
    }
    
    .hero-actions {
      display: flex;
      gap: 16px;
      
      button {
        height: 48px;
        font-size: 16px;
        font-weight: 500;
        min-width: 150px;
      }
    }
  }
  
  .hero-image {
    img {
      width: 100%;
      height: auto;
      border-radius: 12px;
      box-shadow: 0 20px 40px rgba(0, 0, 0, 0.2);
    }
  }
}

.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 20px;
}

.features-section {
  padding: 80px 0;
  background: #f8f9fa;
  
  h2 {
    text-align: center;
    font-size: 36px;
    font-weight: 600;
    margin-bottom: 60px;
    color: #333;
  }
  
  .feature-card {
    height: 100%;
    text-align: center;
    border-radius: 12px;
    transition: all 0.3s ease;
    
    &:hover {
      transform: translateY(-8px);
      box-shadow: 0 12px 32px rgba(0, 0, 0, 0.15);
    }
    
    .feature-content {
      padding: 20px;
      
      .feature-icon {
        font-size: 48px;
        color: #1890ff;
        margin-bottom: 20px;
        display: block;
      }
      
      h3 {
        font-size: 20px;
        font-weight: 600;
        margin-bottom: 12px;
        color: #333;
      }
      
      p {
        color: #666;
        line-height: 1.6;
      }
    }
  }
}

.how-it-works-section {
  padding: 80px 0;
  background: white;
  
  h2 {
    text-align: center;
    font-size: 36px;
    font-weight: 600;
    margin-bottom: 60px;
    color: #333;
  }
  
  .step {
    text-align: center;
    
    .step-number {
      width: 60px;
      height: 60px;
      background: #1890ff;
      color: white;
      border-radius: 50%;
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 24px;
      font-weight: 600;
      margin: 0 auto 24px;
    }
    
    h3 {
      font-size: 20px;
      font-weight: 600;
      margin-bottom: 12px;
      color: #333;
    }
    
    p {
      color: #666;
      line-height: 1.6;
    }
  }
}

.cta-section {
  padding: 80px 0;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  
  .cta-content {
    text-align: center;
    
    h2 {
      font-size: 36px;
      font-weight: 600;
      margin-bottom: 16px;
      color: white;
    }
    
    p {
      font-size: 18px;
      margin-bottom: 32px;
      opacity: 0.9;
    }
    
    .cta-actions {
      display: flex;
      justify-content: center;
      gap: 16px;
      
      button {
        height: 48px;
        font-size: 16px;
        font-weight: 500;
        min-width: 150px;
      }
    }
  }
}

.footer {
  background: #001529;
  color: white;
  padding: 60px 0 0;
  
  .footer-content {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 40px;
    margin-bottom: 40px;
    
    .footer-section {
      h4 {
        color: white;
        font-size: 18px;
        font-weight: 600;
        margin-bottom: 20px;
      }
      
      p {
        color: rgba(255, 255, 255, 0.7);
        line-height: 1.6;
      }
      
      ul {
        list-style: none;
        padding: 0;
        margin: 0;
        
        li {
          margin-bottom: 12px;
          color: rgba(255, 255, 255, 0.7);
          
          a {
            color: rgba(255, 255, 255, 0.7);
            text-decoration: none;
            transition: color 0.3s ease;
            
            &:hover {
              color: #1890ff;
            }
          }
          
          span {
            margin-right: 8px;
          }
        }
      }
      
      .social-links {
        display: flex;
        gap: 12px;
        
        a {
          display: flex;
          align-items: center;
          justify-content: center;
          width: 40px;
          height: 40px;
          background: rgba(255, 255, 255, 0.1);
          border-radius: 50%;
          color: white;
          text-decoration: none;
          transition: all 0.3s ease;
          
          &:hover {
            background: #1890ff;
            transform: translateY(-2px);
          }
        }
      }
    }
  }
  
  .footer-bottom {
    border-top: 1px solid rgba(255, 255, 255, 0.1);
    padding: 20px 0;
    text-align: center;
    
    p {
      color: rgba(255, 255, 255, 0.7);
      margin: 0;
    }
  }
}

// Responsive design
@media (max-width: 768px) {
  .hero-section {
    padding: 60px 15px;
    
    .hero-content {
      grid-template-columns: 1fr;
      gap: 40px;
      text-align: center;
      
      h1 {
        font-size: 32px;
      }
      
      p {
        font-size: 16px;
      }
      
      .hero-actions {
        justify-content: center;
        flex-wrap: wrap;
        
        button {
          width: 100%;
          max-width: 200px;
        }
      }
    }
  }
  
  .features-section,
  .how-it-works-section,
  .cta-section {
    padding: 60px 0;
    
    h2 {
      font-size: 28px;
      margin-bottom: 40px;
    }
  }
  
  .cta-section .cta-actions {
    flex-direction: column;
    align-items: center;
    
    button {
      width: 100%;
      max-width: 250px;
    }
  }
  
  .footer {
    padding: 40px 0 0;
    
    .footer-content {
      grid-template-columns: 1fr;
      gap: 30px;
    }
  }
}

@media (max-width: 576px) {
  .hero-section {
    padding: 40px 10px;
    
    .hero-content h1 {
      font-size: 28px;
    }
  }
  
  .features-section h2,
  .how-it-works-section h2,
  .cta-section h2 {
    font-size: 24px;
  }
}
