import { Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { Observable } from 'rxjs';
import { map } from 'rxjs/operators';
import { BaseHttpService } from './base-http.service';
import { 
  VehicleType, 
  CreateVehicleTypeRequest, 
  UpdateVehicleTypeRequest,
  ApiResponse,
  PaginatedResponse,
  BaseFilter
} from '../models';
import { environment } from '../../../environments/environment';

@Injectable({
  providedIn: 'root'
})
export class VehicleTypeService extends BaseHttpService {
  protected baseUrl = environment.vehicle_service;

  constructor(httpClient: HttpClient) {
    super(httpClient);
  }

  getVehicleTypes(filter?: BaseFilter): Observable<PaginatedResponse<VehicleType>> {
    return this.getPaginated<VehicleType>('/vehicle-types', filter).pipe(
      map(response => response.data)
    );
  }

  getAllVehicleTypes(): Observable<VehicleType[]> {
    return this.get<VehicleType[]>('/vehicle-types/all').pipe(
      map(response => response.data)
    );
  }

  getVehicleType(id: number): Observable<VehicleType> {
    return this.get<VehicleType>(`/vehicle-types/${id}`).pipe(
      map(response => response.data)
    );
  }

  createVehicleType(vehicleType: CreateVehicleTypeRequest): Observable<VehicleType> {
    return this.post<VehicleType>('/vehicle-types', vehicleType).pipe(
      map(response => response.data)
    );
  }

  updateVehicleType(vehicleType: UpdateVehicleTypeRequest): Observable<VehicleType> {
    return this.put<VehicleType>(`/vehicle-types/${vehicleType.id}`, vehicleType).pipe(
      map(response => response.data)
    );
  }

  deleteVehicleType(id: number): Observable<boolean> {
    return this.delete<any>(`/vehicle-types/${id}`).pipe(
      map(response => response.success)
    );
  }
}
