<app-page-header 
  title="Customer Management" 
  subtitle="Manage customer information and driver licenses">
  
  <div nz-page-header-extra>
    <button nz-button nzType="primary" routerLink="/customers/create">
      <span nz-icon nzType="plus"></span>
      Add Customer
    </button>
  </div>
</app-page-header>

<nz-card>
  <form nz-form [formGroup]="filterForm" class="filter-form">
    <div nz-row [nzGutter]="[16, 16]">
      <div nz-col nzXs="24" nzSm="12" nzMd="8">
        <nz-form-item>
          <nz-form-label>Search</nz-form-label>
          <nz-form-control>
            <input nz-input placeholder="Name, email, phone, ID card..." formControlName="search">
          </nz-form-control>
        </nz-form-item>
      </div>
      
      <div nz-col nzXs="24" nzSm="12" nzMd="8">
        <nz-form-item>
          <nz-form-label>Driver License</nz-form-label>
          <nz-form-control>
            <nz-select formControlName="hasDriverLicense" nzPlaceHolder="Select license status" nzAllowClear>
              <nz-option nzValue="true" nzLabel="Has License"></nz-option>
              <nz-option nzValue="false" nzLabel="No License"></nz-option>
            </nz-select>
          </nz-form-control>
        </nz-form-item>
      </div>
      
      <div nz-col nzXs="24" nzSm="12" nzMd="8">
        <nz-form-item>
          <nz-form-label>&nbsp;</nz-form-label>
          <nz-form-control>
            <button nz-button nzType="default" (click)="resetFilters()">
              <span nz-icon nzType="reload"></span>
              Reset
            </button>
          </nz-form-control>
        </nz-form-item>
      </div>
    </div>
  </form>
</nz-card>

<nz-card style="margin-top: 16px;">
  <nz-table 
    #customerTable
    [nzData]="customers"
    [nzLoading]="loading"
    [nzTotal]="total"
    [nzPageSize]="pageSize"
    [nzPageIndex]="pageIndex"
    [nzShowSizeChanger]="true"
    [nzShowQuickJumper]="true"
    [nzShowTotal]="totalTemplate"
    (nzPageIndexChange)="onPageChange($event)"
    (nzPageSizeChange)="onPageSizeChange($event)">
    
    <thead>
      <tr>
        <th>Full Name</th>
        <th>Contact Info</th>
        <th>ID Card</th>
        <th>Driver License</th>
        <th>License Status</th>
        <th>Actions</th>
      </tr>
    </thead>
    
    <tbody>
      <tr *ngFor="let customer of customers">
        <td>
          <strong>{{ customer.fullName }}</strong>
        </td>
        <td>
          <div>{{ customer.email }}</div>
          <div style="color: #666;">{{ customer.phone }}</div>
        </td>
        <td>{{ customer.idCardNumber }}</td>
        <td>
          <div *ngIf="hasDriverLicense(customer)">
            {{ customer.driverLicenseNumber }}
            <div style="font-size: 12px; color: #666;">
              Expires: {{ customer.licenseExpiryDate | date:'dd/MM/yyyy' }}
            </div>
          </div>
          <span *ngIf="!hasDriverLicense(customer)" style="color: #999;">No License</span>
        </td>
        <td>
          <nz-tag *ngIf="hasDriverLicense(customer)" 
                  [nzColor]="isLicenseExpired(customer) ? 'red' : 'green'">
            {{ isLicenseExpired(customer) ? 'Expired' : 'Valid' }}
          </nz-tag>
          <nz-tag *ngIf="!hasDriverLicense(customer)" nzColor="default">
            No License
          </nz-tag>
        </td>
        <td>
          <button nz-button nzType="link" nzSize="small" [routerLink]="['/customers/edit', customer.id]">
            <span nz-icon nzType="edit"></span>
            Edit
          </button>
          <nz-divider nzType="vertical"></nz-divider>
          <button nz-button nzType="link" nzSize="small" nzDanger (click)="showDeleteModal(customer)">
            <span nz-icon nzType="delete"></span>
            Delete
          </button>
        </td>
      </tr>
    </tbody>
  </nz-table>
  
  <ng-template #totalTemplate let-total let-range="range">
    {{ range[0] }}-{{ range[1] }} of {{ total }} customers
  </ng-template>
</nz-card>

<app-confirmation-modal
  [(visible)]="deleteModalVisible"
  title="Delete Customer"
  content="Are you sure you want to delete this customer? This action cannot be undone."
  okText="Delete"
  okType="primary"
  [loading]="deleteLoading"
  (onOk)="confirmDelete()"
  (onCancel)="cancelDelete()">
</app-confirmation-modal>
