<div class="login-container">
  <div class="login-card">
    <nz-card nzTitle="Welcome Back" class="login-form-card">
      <div class="subtitle">Sign in to your account to continue</div>
      
      <form nz-form [formGroup]="loginForm" (ngSubmit)="onSubmit()">
        <nz-form-item>
          <nz-form-label [nzSpan]="24" nzRequired>Email or Phone</nz-form-label>
          <nz-form-control [nzSpan]="24" nzErrorTip="Please enter your email or phone number">
            <input 
              nz-input 
              formControlName="username" 
              placeholder="Enter email or phone number"
              [nzSize]="'large'"
              prefix="user">
          </nz-form-control>
        </nz-form-item>

        <nz-form-item>
          <nz-form-label [nzSpan]="24" nzRequired>Password</nz-form-label>
          <nz-form-control [nzSpan]="24" nzErrorTip="Please enter your password">
            <nz-input-group [nzSuffix]="suffixTemplate">
              <input 
                [type]="passwordVisible ? 'text' : 'password'"
                nz-input 
                formControlName="password" 
                placeholder="Enter password"
                [nzSize]="'large'"
                prefix="lock">
            </nz-input-group>
            <ng-template #suffixTemplate>
              <span 
                nz-icon 
                [nzType]="passwordVisible ? 'eye-invisible' : 'eye'" 
                (click)="togglePasswordVisibility()"
                class="password-toggle">
              </span>
            </ng-template>
          </nz-form-control>
        </nz-form-item>

        <nz-form-item>
          <nz-form-control [nzSpan]="24">
            <div class="login-options">
              <label nz-checkbox formControlName="rememberMe">
                Remember me
              </label>
              <a href="#" class="forgot-password">Forgot password?</a>
            </div>
          </nz-form-control>
        </nz-form-item>

        <nz-form-item>
          <nz-form-control [nzSpan]="24">
            <button 
              nz-button 
              nzType="primary" 
              nzSize="large"
              [nzLoading]="isLoading"
              [disabled]="!loginForm.valid"
              class="login-button">
              Sign In
            </button>
          </nz-form-control>
        </nz-form-item>
      </form>

      <div class="divider">
        <span>or</span>
      </div>

      <div class="register-section">
        <p>Don't have an account?</p>
        <button 
          nz-button 
          nzType="default" 
          nzSize="large"
          (click)="navigateToRegister()"
          class="register-button">
          Create Account
        </button>
      </div>

      <div class="back-to-home">
        <button nz-button nzType="link" (click)="navigateToHome()">
          <span nz-icon nzType="arrow-left"></span>
          Back to Home
        </button>
      </div>
    </nz-card>
  </div>
</div>
