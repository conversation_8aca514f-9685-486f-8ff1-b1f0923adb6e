<div class="register-container">
  <div class="register-card">
    <nz-card nzTitle="Create Your Account" class="register-form-card">
      <div class="subtitle">Join our car rental service and start your journey</div>
      
      <form nz-form [formGroup]="registerForm" (ngSubmit)="onSubmit()">
        <!-- Personal Information -->
        <div class="form-section">
          <h4>Personal Information</h4>
          
          <nz-form-item>
            <nz-form-label [nzSpan]="24" nzRequired>Full Name</nz-form-label>
            <nz-form-control [nzSpan]="24" nzErrorTip="Please enter your full name (min 2 characters)">
              <input nz-input formControlName="fullName" placeholder="Enter your full name">
            </nz-form-control>
          </nz-form-item>

          <div nz-row [nzGutter]="16">
            <div nz-col nzSpan="12">
              <nz-form-item>
                <nz-form-label [nzSpan]="24" nzRequired>Email</nz-form-label>
                <nz-form-control [nzSpan]="24" nzErrorTip="Please enter a valid email address">
                  <input nz-input formControlName="email" placeholder="Enter email address" type="email">
                </nz-form-control>
              </nz-form-item>
            </div>
            <div nz-col nzSpan="12">
              <nz-form-item>
                <nz-form-label [nzSpan]="24" nzRequired>Phone Number</nz-form-label>
                <nz-form-control [nzSpan]="24" nzErrorTip="Please enter a valid Vietnamese phone number">
                  <input nz-input formControlName="phone" placeholder="e.g., 0901234567">
                </nz-form-control>
              </nz-form-item>
            </div>
          </div>

          <nz-form-item>
            <nz-form-label [nzSpan]="24" nzRequired>ID Card Number</nz-form-label>
            <nz-form-control [nzSpan]="24" nzErrorTip="Please enter a valid ID card number (9 or 12 digits)">
              <input nz-input formControlName="idCardNumber" placeholder="e.g., *********">
            </nz-form-control>
          </nz-form-item>

          <nz-form-item>
            <nz-form-label [nzSpan]="24" nzRequired>Address</nz-form-label>
            <nz-form-control [nzSpan]="24" nzErrorTip="Please enter your address">
              <textarea nz-input formControlName="address" placeholder="Enter your full address" rows="2"></textarea>
            </nz-form-control>
          </nz-form-item>
        </div>

        <!-- Account Security -->
        <div class="form-section">
          <h4>Account Security</h4>
          
          <div nz-row [nzGutter]="16">
            <div nz-col nzSpan="12">
              <nz-form-item>
                <nz-form-label [nzSpan]="24" nzRequired>Password</nz-form-label>
                <nz-form-control [nzSpan]="24" nzErrorTip="Password must be at least 6 characters">
                  <nz-input-group [nzSuffix]="passwordSuffixTemplate">
                    <input 
                      [type]="passwordVisible ? 'text' : 'password'"
                      nz-input 
                      formControlName="password" 
                      placeholder="Enter password">
                  </nz-input-group>
                  <ng-template #passwordSuffixTemplate>
                    <span 
                      nz-icon 
                      [nzType]="passwordVisible ? 'eye-invisible' : 'eye'" 
                      (click)="togglePasswordVisibility()"
                      class="password-toggle">
                    </span>
                  </ng-template>
                </nz-form-control>
              </nz-form-item>
            </div>
            <div nz-col nzSpan="12">
              <nz-form-item>
                <nz-form-label [nzSpan]="24" nzRequired>Confirm Password</nz-form-label>
                <nz-form-control [nzSpan]="24" nzErrorTip="Passwords do not match">
                  <nz-input-group [nzSuffix]="confirmPasswordSuffixTemplate">
                    <input 
                      [type]="confirmPasswordVisible ? 'text' : 'password'"
                      nz-input 
                      formControlName="confirmPassword" 
                      placeholder="Confirm password">
                  </nz-input-group>
                  <ng-template #confirmPasswordSuffixTemplate>
                    <span 
                      nz-icon 
                      [nzType]="confirmPasswordVisible ? 'eye-invisible' : 'eye'" 
                      (click)="toggleConfirmPasswordVisibility()"
                      class="password-toggle">
                    </span>
                  </ng-template>
                </nz-form-control>
              </nz-form-item>
            </div>
          </div>
        </div>

        <!-- Driver License (Optional) -->
        <div class="form-section">
          <h4>Driver License (Optional)</h4>
          
          <nz-form-item>
            <nz-form-control [nzSpan]="24">
              <label nz-checkbox formControlName="hasDriverLicense">
                I have a valid driver license
              </label>
            </nz-form-control>
          </nz-form-item>

          <div *ngIf="registerForm.get('hasDriverLicense')?.value">
            <nz-form-item>
              <nz-form-label [nzSpan]="24" nzRequired>Driver License Number</nz-form-label>
              <nz-form-control [nzSpan]="24" nzErrorTip="Please enter a valid license number (12 digits)">
                <input nz-input formControlName="driverLicenseNumber" placeholder="e.g., *********012">
              </nz-form-control>
            </nz-form-item>

            <div nz-row [nzGutter]="16">
              <div nz-col nzSpan="12">
                <nz-form-item>
                  <nz-form-label [nzSpan]="24" nzRequired>Issue Date</nz-form-label>
                  <nz-form-control [nzSpan]="24" nzErrorTip="Please select issue date">
                    <nz-date-picker 
                      formControlName="licenseIssueDate" 
                      [nzDisabledDate]="disabledDate"
                      nzPlaceHolder="Select issue date"
                      style="width: 100%">
                    </nz-date-picker>
                  </nz-form-control>
                </nz-form-item>
              </div>
              <div nz-col nzSpan="12">
                <nz-form-item>
                  <nz-form-label [nzSpan]="24" nzRequired>Expiry Date</nz-form-label>
                  <nz-form-control [nzSpan]="24" nzErrorTip="Please select expiry date">
                    <nz-date-picker 
                      formControlName="licenseExpiryDate" 
                      [nzDisabledDate]="disabledExpiryDate"
                      nzPlaceHolder="Select expiry date"
                      style="width: 100%">
                    </nz-date-picker>
                  </nz-form-control>
                </nz-form-item>
              </div>
            </div>
          </div>
        </div>

        <!-- Terms and Conditions -->
        <nz-form-item>
          <nz-form-control [nzSpan]="24" nzErrorTip="Please agree to the terms and conditions">
            <label nz-checkbox formControlName="agreeToTerms">
              I agree to the <a href="#" target="_blank">Terms and Conditions</a> and <a href="#" target="_blank">Privacy Policy</a>
            </label>
          </nz-form-control>
        </nz-form-item>

        <!-- Submit Button -->
        <nz-form-item>
          <nz-form-control [nzSpan]="24">
            <button 
              nz-button 
              nzType="primary" 
              nzSize="large"
              [nzLoading]="isLoading"
              [disabled]="!registerForm.valid"
              class="register-button">
              Create Account
            </button>
          </nz-form-control>
        </nz-form-item>

        <!-- Login Link -->
        <div class="login-link">
          Already have an account? <a routerLink="/customer/login">Sign in here</a>
        </div>
      </form>
    </nz-card>
  </div>
</div>
