import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { RouterModule } from '@angular/router';
import { ReactiveFormsModule, FormBuilder, FormGroup } from '@angular/forms';
import { NzTableModule } from 'ng-zorro-antd/table';
import { NzButtonModule } from 'ng-zorro-antd/button';
import { NzInputModule } from 'ng-zorro-antd/input';
import { NzSelectModule } from 'ng-zorro-antd/select';
import { NzTagModule } from 'ng-zorro-antd/tag';
import { NzCardModule } from 'ng-zorro-antd/card';
import { NzFormModule } from 'ng-zorro-antd/form';
import { NzIconModule } from 'ng-zorro-antd/icon';
import { NzDividerModule } from 'ng-zorro-antd/divider';
import { NzMessageService } from 'ng-zorro-antd/message';
import { Observable, BehaviorSubject } from 'rxjs';
import { debounceTime, distinctUntilChanged, switchMap } from 'rxjs/operators';

import { PageHeaderComponent } from '../../../shared/components/page-header/page-header.component';
import { ConfirmationModalComponent } from '../../../shared/components/confirmation-modal/confirmation-modal.component';
import { StatusColorPipe } from '../../../shared/pipes/status-color.pipe';
import { CurrencyFormatPipe } from '../../../shared/pipes/currency-format.pipe';
import { VehicleService, VehicleTypeService } from '../../../core/services';
import { Vehicle, VehicleType, VehicleStatus, VehicleFilter, PaginatedResponse } from '../../../core/models';

@Component({
  selector: 'app-vehicle-list',
  standalone: true,
  imports: [
    CommonModule,
    RouterModule,
    ReactiveFormsModule,
    NzTableModule,
    NzButtonModule,
    NzInputModule,
    NzSelectModule,
    NzTagModule,
    NzCardModule,
    NzFormModule,
    NzIconModule,
    NzDividerModule,
    PageHeaderComponent,
    ConfirmationModalComponent,
    StatusColorPipe,
    CurrencyFormatPipe
  ],
  templateUrl: './vehicle-list.component.html',
  styleUrls: ['./vehicle-list.component.scss']
})
export class VehicleListComponent implements OnInit {
  vehicles: Vehicle[] = [];
  vehicleTypes: VehicleType[] = [];
  loading = false;
  total = 0;
  pageSize = 10;
  pageIndex = 1;
  
  filterForm!: FormGroup;
  private filterSubject = new BehaviorSubject<VehicleFilter>({});
  
  deleteModalVisible = false;
  deleteLoading = false;
  vehicleToDelete: Vehicle | null = null;
  
  vehicleStatusOptions = Object.values(VehicleStatus);

  constructor(
    private fb: FormBuilder,
    private vehicleService: VehicleService,
    private vehicleTypeService: VehicleTypeService,
    private message: NzMessageService
  ) {}

  ngOnInit(): void {
    this.initFilterForm();
    this.loadVehicleTypes();
    this.setupFilterSubscription();
    this.loadVehicles();
  }

  private initFilterForm(): void {
    this.filterForm = this.fb.group({
      search: [''],
      vehicleTypeId: [null],
      status: [null],
      seatCount: [null]
    });
  }

  private setupFilterSubscription(): void {
    this.filterForm.valueChanges.pipe(
      debounceTime(300),
      distinctUntilChanged()
    ).subscribe(values => {
      const filter: VehicleFilter = {
        ...values,
        page: 1,
        limit: this.pageSize
      };
      this.filterSubject.next(filter);
      this.pageIndex = 1;
    });

    this.filterSubject.pipe(
      switchMap(filter => {
        this.loading = true;
        return this.vehicleService.getVehicles(filter);
      })
    ).subscribe({
      next: (response: PaginatedResponse<Vehicle>) => {
        this.vehicles = response.data;
        this.total = response.total;
        this.loading = false;
      },
      error: (error) => {
        this.message.error('Failed to load vehicles');
        this.loading = false;
      }
    });
  }

  private loadVehicleTypes(): void {
    this.vehicleTypeService.getAllVehicleTypes().subscribe({
      next: (types) => {
        this.vehicleTypes = types;
      },
      error: (error) => {
        this.message.error('Failed to load vehicle types');
      }
    });
  }

  private loadVehicles(): void {
    const filter: VehicleFilter = {
      ...this.filterForm.value,
      page: this.pageIndex,
      limit: this.pageSize
    };
    this.filterSubject.next(filter);
  }

  onPageChange(pageIndex: number): void {
    this.pageIndex = pageIndex;
    this.loadVehicles();
  }

  onPageSizeChange(pageSize: number): void {
    this.pageSize = pageSize;
    this.pageIndex = 1;
    this.loadVehicles();
  }

  resetFilters(): void {
    this.filterForm.reset();
  }

  showDeleteModal(vehicle: Vehicle): void {
    this.vehicleToDelete = vehicle;
    this.deleteModalVisible = true;
  }

  confirmDelete(): void {
    if (this.vehicleToDelete) {
      this.deleteLoading = true;
      this.vehicleService.deleteVehicle(this.vehicleToDelete.id).subscribe({
        next: () => {
          this.message.success('Vehicle deleted successfully');
          this.deleteModalVisible = false;
          this.deleteLoading = false;
          this.vehicleToDelete = null;
          this.loadVehicles();
        },
        error: (error) => {
          this.message.error('Failed to delete vehicle');
          this.deleteLoading = false;
        }
      });
    }
  }

  cancelDelete(): void {
    this.deleteModalVisible = false;
    this.vehicleToDelete = null;
  }
}
