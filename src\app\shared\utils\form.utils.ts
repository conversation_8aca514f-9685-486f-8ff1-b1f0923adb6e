import { FormGroup, FormControl, AbstractControl } from '@angular/forms';

export class FormUtils {
  static markFormGroupTouched(formGroup: FormGroup): void {
    Object.keys(formGroup.controls).forEach(key => {
      const control = formGroup.get(key);
      if (control instanceof FormControl) {
        control.markAsTouched();
      } else if (control instanceof FormGroup) {
        this.markFormGroupTouched(control);
      }
    });
  }

  static getFormValidationErrors(formGroup: FormGroup): string[] {
    const errors: string[] = [];
    
    Object.keys(formGroup.controls).forEach(key => {
      const control = formGroup.get(key);
      if (control && control.errors && control.touched) {
        Object.keys(control.errors).forEach(errorKey => {
          errors.push(this.getErrorMessage(key, errorKey, control.errors![errorKey]));
        });
      }
    });
    
    return errors;
  }

  static getErrorMessage(fieldName: string, errorType: string, errorValue: any): string {
    const fieldDisplayName = this.getFieldDisplayName(fieldName);
    
    switch (errorType) {
      case 'required':
        return `${fieldDisplayName} is required`;
      case 'email':
        return `${fieldDisplayName} must be a valid email address`;
      case 'minlength':
        return `${fieldDisplayName} must be at least ${errorValue.requiredLength} characters`;
      case 'maxlength':
        return `${fieldDisplayName} cannot exceed ${errorValue.requiredLength} characters`;
      case 'pattern':
        return `${fieldDisplayName} format is invalid`;
      case 'min':
        return `${fieldDisplayName} must be at least ${errorValue.min}`;
      case 'max':
        return `${fieldDisplayName} cannot exceed ${errorValue.max}`;
      default:
        return `${fieldDisplayName} is invalid`;
    }
  }

  private static getFieldDisplayName(fieldName: string): string {
    // Convert camelCase to readable format
    return fieldName
      .replace(/([A-Z])/g, ' $1')
      .replace(/^./, str => str.toUpperCase())
      .trim();
  }

  static resetForm(formGroup: FormGroup): void {
    formGroup.reset();
    Object.keys(formGroup.controls).forEach(key => {
      const control = formGroup.get(key);
      if (control) {
        control.setErrors(null);
        control.markAsUntouched();
        control.markAsPristine();
      }
    });
  }
}
