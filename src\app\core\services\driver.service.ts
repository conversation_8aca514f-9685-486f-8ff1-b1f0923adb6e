import { Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { Observable } from 'rxjs';
import { map } from 'rxjs/operators';
import { BaseHttpService } from './base-http.service';
import { 
  Driver, 
  CreateDriverRequest, 
  UpdateDriverRequest,
  DriverFilter,
  DriverStatus,
  ApiResponse,
  PaginatedResponse
} from '../models';
import { environment } from '../../../environments/environment';

@Injectable({
  providedIn: 'root'
})
export class DriverService extends BaseHttpService {
  protected baseUrl = environment.driver_service;

  constructor(httpClient: HttpClient) {
    super(httpClient);
  }

  getDrivers(filter?: DriverFilter): Observable<PaginatedResponse<Driver>> {
    return this.getPaginated<Driver>('/drivers', filter).pipe(
      map(response => response.data)
    );
  }

  getAvailableDrivers(startDate: Date, endDate: Date): Observable<Driver[]> {
    const params = { startDate, endDate };
    return this.get<Driver[]>('/drivers/available', params).pipe(
      map(response => response.data)
    );
  }

  getDriver(id: number): Observable<Driver> {
    return this.get<Driver>(`/drivers/${id}`).pipe(
      map(response => response.data)
    );
  }

  createDriver(driver: CreateDriverRequest): Observable<Driver> {
    return this.post<Driver>('/drivers', driver).pipe(
      map(response => response.data)
    );
  }

  updateDriver(driver: UpdateDriverRequest): Observable<Driver> {
    return this.put<Driver>(`/drivers/${driver.id}`, driver).pipe(
      map(response => response.data)
    );
  }

  updateDriverStatus(id: number, status: DriverStatus): Observable<Driver> {
    return this.put<Driver>(`/drivers/${id}/status`, { status }).pipe(
      map(response => response.data)
    );
  }

  deleteDriver(id: number): Observable<boolean> {
    return this.delete<any>(`/drivers/${id}`).pipe(
      map(response => response.success)
    );
  }

  searchDrivers(query: string): Observable<Driver[]> {
    return this.get<Driver[]>('/drivers/search', { query }).pipe(
      map(response => response.data)
    );
  }
}
