import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { RouterModule, Router } from '@angular/router';
import { ReactiveFormsModule, FormBuilder, FormGroup, Validators } from '@angular/forms';
import { NzFormModule } from 'ng-zorro-antd/form';
import { NzInputModule } from 'ng-zorro-antd/input';
import { NzButtonModule } from 'ng-zorro-antd/button';
import { NzCardModule } from 'ng-zorro-antd/card';
import { NzCheckboxModule } from 'ng-zorro-antd/checkbox';
import { NzDatePickerModule } from 'ng-zorro-antd/date-picker';
import { NzIconModule } from 'ng-zorro-antd/icon';
import { NzMessageService } from 'ng-zorro-antd/message';
import { CustomerAuthService } from '../../../../core/services/customer-auth.service';
import { CustomerRegistrationRequest } from '../../../../core/models';
import { CustomValidators } from '../../../../shared/validators/custom.validators';

@Component({
  selector: 'app-customer-register',
  standalone: true,
  imports: [
    CommonModule,
    RouterModule,
    ReactiveFormsModule,
    NzFormModule,
    NzInputModule,
    NzButtonModule,
    NzCardModule,
    NzCheckboxModule,
    NzDatePickerModule,
    NzIconModule
  ],
  templateUrl: './register.component.html',
  styleUrls: ['./register.component.scss']
})
export class CustomerRegisterComponent implements OnInit {
  registerForm!: FormGroup;
  isLoading = false;
  passwordVisible = false;
  confirmPasswordVisible = false;

  constructor(
    private fb: FormBuilder,
    private customerAuthService: CustomerAuthService,
    private router: Router,
    private message: NzMessageService
  ) {}

  ngOnInit(): void {
    this.initForm();
    
    // Redirect if already authenticated
    if (this.customerAuthService.isAuthenticated()) {
      this.router.navigate(['/customer/dashboard']);
    }
  }

  private initForm(): void {
    this.registerForm = this.fb.group({
      fullName: ['', [Validators.required, Validators.minLength(2)]],
      email: ['', [Validators.required, Validators.email]],
      phone: ['', [Validators.required, CustomValidators.phoneNumber]],
      address: ['', [Validators.required]],
      idCardNumber: ['', [Validators.required, CustomValidators.idCardNumber]],
      password: ['', [Validators.required, Validators.minLength(6)]],
      confirmPassword: ['', [Validators.required]],
      hasDriverLicense: [false],
      driverLicenseNumber: [''],
      licenseIssueDate: [null],
      licenseExpiryDate: [null],
      agreeToTerms: [false, [Validators.requiredTrue]]
    });

    // Add password confirmation validator
    this.registerForm.get('confirmPassword')?.setValidators([
      Validators.required,
      this.confirmPasswordValidator.bind(this)
    ]);

    // Watch driver license checkbox
    this.registerForm.get('hasDriverLicense')?.valueChanges.subscribe(hasLicense => {
      const licenseNumberControl = this.registerForm.get('driverLicenseNumber');
      const issueControl = this.registerForm.get('licenseIssueDate');
      const expiryControl = this.registerForm.get('licenseExpiryDate');

      if (hasLicense) {
        licenseNumberControl?.setValidators([Validators.required, CustomValidators.driverLicenseNumber]);
        issueControl?.setValidators([Validators.required, CustomValidators.pastDate]);
        expiryControl?.setValidators([Validators.required, CustomValidators.futureDate]);
      } else {
        licenseNumberControl?.clearValidators();
        issueControl?.clearValidators();
        expiryControl?.clearValidators();
        licenseNumberControl?.setValue('');
        issueControl?.setValue(null);
        expiryControl?.setValue(null);
      }

      licenseNumberControl?.updateValueAndValidity();
      issueControl?.updateValueAndValidity();
      expiryControl?.updateValueAndValidity();
    });
  }

  private confirmPasswordValidator(control: any) {
    const password = this.registerForm?.get('password')?.value;
    const confirmPassword = control.value;
    
    if (password !== confirmPassword) {
      return { confirmPassword: true };
    }
    return null;
  }

  onSubmit(): void {
    if (this.registerForm.valid) {
      this.isLoading = true;
      
      const formValue = this.registerForm.value;
      const registrationRequest: CustomerRegistrationRequest = {
        fullName: formValue.fullName,
        email: formValue.email,
        phone: formValue.phone,
        address: formValue.address,
        idCardNumber: formValue.idCardNumber,
        password: formValue.password,
        driverLicenseNumber: formValue.hasDriverLicense ? formValue.driverLicenseNumber : undefined,
        licenseIssueDate: formValue.hasDriverLicense ? formValue.licenseIssueDate : undefined,
        licenseExpiryDate: formValue.hasDriverLicense ? formValue.licenseExpiryDate : undefined
      };

      this.customerAuthService.register(registrationRequest).subscribe({
        next: (response) => {
          this.message.success('Registration successful! Welcome to our car rental service.');
          this.router.navigate(['/customer/dashboard']);
        },
        error: (error) => {
          this.isLoading = false;
          this.message.error(error.error?.message || 'Registration failed. Please try again.');
        },
        complete: () => {
          this.isLoading = false;
        }
      });
    } else {
      this.markFormGroupTouched();
    }
  }

  private markFormGroupTouched(): void {
    Object.keys(this.registerForm.controls).forEach(key => {
      const control = this.registerForm.get(key);
      if (control) {
        control.markAsTouched();
      }
    });
  }

  togglePasswordVisibility(): void {
    this.passwordVisible = !this.passwordVisible;
  }

  toggleConfirmPasswordVisibility(): void {
    this.confirmPasswordVisible = !this.confirmPasswordVisible;
  }

  disabledDate = (current: Date): boolean => {
    const today = new Date();
    today.setHours(0, 0, 0, 0);
    return current && current > today;
  };

  disabledExpiryDate = (current: Date): boolean => {
    const today = new Date();
    today.setHours(0, 0, 0, 0);
    return current && current <= today;
  };
}
