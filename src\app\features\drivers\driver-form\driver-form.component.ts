import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { RouterModule, ActivatedRoute, Router } from '@angular/router';
import { ReactiveFormsModule, FormBuilder, FormGroup, Validators } from '@angular/forms';
import { NzFormModule } from 'ng-zorro-antd/form';
import { NzInputModule } from 'ng-zorro-antd/input';
import { NzButtonModule } from 'ng-zorro-antd/button';
import { NzCardModule } from 'ng-zorro-antd/card';
import { NzSelectModule } from 'ng-zorro-antd/select';
import { NzDatePickerModule } from 'ng-zorro-antd/date-picker';
import { NzMessageService } from 'ng-zorro-antd/message';
import { PageHeaderComponent } from '../../../shared/components/page-header/page-header.component';
import { DriverService } from '../../../core/services';
import { Driver, CreateDriverRequest, UpdateDriverRequest, DriverStatus } from '../../../core/models';
import { CustomValidators } from '../../../shared/validators/custom.validators';

@Component({
  selector: 'app-driver-form',
  standalone: true,
  imports: [
    CommonModule,
    RouterModule,
    ReactiveFormsModule,
    NzFormModule,
    NzInputModule,
    NzButtonModule,
    NzCardModule,
    NzSelectModule,
    NzDatePickerModule,
    PageHeaderComponent
  ],
  templateUrl: './driver-form.component.html',
  styleUrls: ['./driver-form.component.scss']
})
export class DriverFormComponent implements OnInit {
  driverForm!: FormGroup;
  isEditMode = false;
  driverId: number | null = null;
  loading = false;
  submitting = false;

  driverStatusOptions = Object.values(DriverStatus);

  constructor(
    private fb: FormBuilder,
    private route: ActivatedRoute,
    private router: Router,
    private driverService: DriverService,
    private message: NzMessageService
  ) {}

  ngOnInit(): void {
    this.initForm();
    this.checkEditMode();
  }

  private initForm(): void {
    this.driverForm = this.fb.group({
      fullName: ['', [Validators.required, Validators.minLength(2)]],
      email: ['', [Validators.required, Validators.email]],
      phone: ['', [Validators.required, CustomValidators.phoneNumber]],
      address: ['', [Validators.required]],
      idCardNumber: ['', [Validators.required, CustomValidators.idCardNumber]],
      driverLicenseNumber: ['', [Validators.required, CustomValidators.driverLicenseNumber]],
      licenseIssueDate: [null, [Validators.required, CustomValidators.pastDate]],
      licenseExpiryDate: [null, [Validators.required, CustomValidators.futureDate]],
      status: [DriverStatus.AVAILABLE]
    });
  }

  private checkEditMode(): void {
    const id = this.route.snapshot.paramMap.get('id');
    if (id) {
      this.isEditMode = true;
      this.driverId = +id;
      this.loadDriver();
    }
  }

  private loadDriver(): void {
    if (this.driverId) {
      this.loading = true;
      this.driverService.getDriver(this.driverId).subscribe({
        next: (driver) => {
          this.driverForm.patchValue({
            ...driver,
            licenseIssueDate: new Date(driver.licenseIssueDate),
            licenseExpiryDate: new Date(driver.licenseExpiryDate)
          });
          this.loading = false;
        },
        error: (error) => {
          this.message.error('Failed to load driver');
          this.loading = false;
        }
      });
    }
  }

  onSubmit(): void {
    if (this.driverForm.valid) {
      this.submitting = true;
      
      const formValue = this.driverForm.value;
      const driverData = {
        ...formValue,
        licenseIssueDate: new Date(formValue.licenseIssueDate),
        licenseExpiryDate: new Date(formValue.licenseExpiryDate)
      };
      
      if (this.isEditMode && this.driverId) {
        const updateRequest: UpdateDriverRequest = {
          id: this.driverId,
          ...driverData
        };
        
        this.driverService.updateDriver(updateRequest).subscribe({
          next: () => {
            this.message.success('Driver updated successfully');
            this.router.navigate(['/drivers']);
          },
          error: (error) => {
            this.message.error('Failed to update driver');
            this.submitting = false;
          }
        });
      } else {
        const createRequest: CreateDriverRequest = driverData;
        
        this.driverService.createDriver(createRequest).subscribe({
          next: () => {
            this.message.success('Driver created successfully');
            this.router.navigate(['/drivers']);
          },
          error: (error) => {
            this.message.error('Failed to create driver');
            this.submitting = false;
          }
        });
      }
    } else {
      this.markFormGroupTouched();
    }
  }

  private markFormGroupTouched(): void {
    Object.keys(this.driverForm.controls).forEach(key => {
      const control = this.driverForm.get(key);
      if (control) {
        control.markAsTouched();
      }
    });
  }

  onCancel(): void {
    this.router.navigate(['/drivers']);
  }

  disabledDate = (current: Date): boolean => {
    const today = new Date();
    today.setHours(0, 0, 0, 0);
    return current && current > today;
  };

  disabledExpiryDate = (current: Date): boolean => {
    const today = new Date();
    today.setHours(0, 0, 0, 0);
    return current && current <= today;
  };
}
