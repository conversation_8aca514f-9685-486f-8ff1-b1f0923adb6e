<app-page-header 
  title="Vehicle Types" 
  subtitle="Manage vehicle type categories">
</app-page-header>

<nz-card>
  <nz-table [nzData]="vehicleTypes" [nzLoading]="loading">
    <thead>
      <tr>
        <th>ID</th>
        <th>Name</th>
        <th>Description</th>
        <th>Actions</th>
      </tr>
    </thead>
    <tbody>
      <tr *ngFor="let type of vehicleTypes">
        <td>{{ type.id }}</td>
        <td><strong>{{ type.nameVehicleType }}</strong></td>
        <td>{{ type.description }}</td>
        <td>
          <button nz-button nzType="link" nzSize="small">
            Edit
          </button>
        </td>
      </tr>
    </tbody>
  </nz-table>
</nz-card>
