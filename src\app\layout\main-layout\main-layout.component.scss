.app-layout {
  min-height: 100vh;
}

.menu-sidebar {
  position: fixed;
  height: 100vh;
  left: 0;
  overflow: auto;
  z-index: 100;
}

.sidebar-logo {
  height: 64px;
  padding: 16px;
  background: rgba(255, 255, 255, 0.3);
  text-align: center;
  
  h3 {
    color: white;
    margin: 0;
    font-weight: 600;
  }
}

.app-header {
  position: fixed;
  z-index: 99;
  width: 100%;
  background: #fff;
  box-shadow: 0 1px 4px rgba(0, 21, 41, 0.08);
  padding: 0 24px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-left: 200px;
  width: calc(100% - 200px);
  
  &.collapsed {
    margin-left: 80px;
    width: calc(100% - 80px);
  }
}

.trigger {
  font-size: 18px;
  line-height: 64px;
  padding: 0 24px;
  cursor: pointer;
  transition: color 0.3s;
  
  &:hover {
    color: #1890ff;
  }
}

.header-right {
  display: flex;
  align-items: center;
}

.user-dropdown {
  display: flex;
  align-items: center;
  cursor: pointer;
  padding: 0 12px;
  
  .user-name {
    margin: 0 8px;
    font-weight: 500;
  }
  
  &:hover {
    background-color: #f5f5f5;
  }
}

.app-content {
  margin: 88px 0 0 200px;
  padding: 24px;
  background: #f0f2f5;
  min-height: calc(100vh - 88px);
  
  &.collapsed {
    margin-left: 80px;
  }
}

// Responsive design
@media (max-width: 768px) {
  .menu-sidebar {
    position: fixed;
    z-index: 1000;
  }
  
  .app-header {
    margin-left: 0;
    width: 100%;
  }
  
  .app-content {
    margin-left: 0;
  }
}
