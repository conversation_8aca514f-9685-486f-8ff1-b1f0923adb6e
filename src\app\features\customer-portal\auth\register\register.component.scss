.register-container {
  min-height: 100vh;
  display: flex;
  justify-content: center;
  align-items: center;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  padding: 20px;
}

.register-card {
  width: 100%;
  max-width: 600px;
}

.register-form-card {
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  border-radius: 12px;
  
  ::ng-deep .ant-card-head {
    text-align: center;
    border-bottom: 1px solid #f0f0f0;
    
    .ant-card-head-title {
      font-size: 28px;
      font-weight: 600;
      color: #1890ff;
    }
  }
  
  ::ng-deep .ant-card-body {
    padding: 32px;
  }
}

.subtitle {
  text-align: center;
  color: #666;
  margin-bottom: 32px;
  font-size: 16px;
}

.form-section {
  margin-bottom: 32px;
  
  h4 {
    color: #333;
    font-size: 18px;
    font-weight: 600;
    margin-bottom: 16px;
    padding-bottom: 8px;
    border-bottom: 2px solid #f0f0f0;
  }
}

.register-button {
  width: 100%;
  height: 48px;
  font-size: 16px;
  font-weight: 500;
}

.password-toggle {
  cursor: pointer;
  color: #8c8c8c;
  
  &:hover {
    color: #1890ff;
  }
}

.login-link {
  text-align: center;
  margin-top: 24px;
  color: #666;
  
  a {
    color: #1890ff;
    text-decoration: none;
    font-weight: 500;
    
    &:hover {
      text-decoration: underline;
    }
  }
}

nz-form-item {
  margin-bottom: 16px;
}

::ng-deep .ant-input-affix-wrapper {
  .ant-input-prefix {
    color: #8c8c8c;
  }
}

// Responsive design
@media (max-width: 768px) {
  .register-container {
    padding: 10px;
  }
  
  .register-form-card {
    ::ng-deep .ant-card-body {
      padding: 20px;
    }
  }
  
  .form-section h4 {
    font-size: 16px;
  }
}
