<div class="bookings-container">
  <!-- Header -->
  <div class="page-header">
    <h1>My Bookings</h1>
    <p>View and manage your rental history</p>
  </div>

  <!-- Filters -->
  <nz-card class="filter-card">
    <form nz-form [formGroup]="filterForm" class="filter-form">
      <div nz-row [nzGutter]="[16, 16]">
        <div nz-col nzXs="24" nzSm="8">
          <nz-form-item>
            <nz-form-label>Status</nz-form-label>
            <nz-form-control>
              <nz-select formControlName="status" nzPlaceHolder="All statuses" nzAllowClear>
                <nz-option *ngFor="let status of contractStatusOptions" [nzValue]="status" [nzLabel]="getBookingStatusText(status)"></nz-option>
              </nz-select>
            </nz-form-control>
          </nz-form-item>
        </div>
        
        <div nz-col nzXs="24" nzSm="8">
          <nz-form-item>
            <nz-form-label>Start Date From</nz-form-label>
            <nz-form-control>
              <nz-date-picker formControlName="startDate" nzPlaceHolder="Select date" style="width: 100%"></nz-date-picker>
            </nz-form-control>
          </nz-form-item>
        </div>
        
        <div nz-col nzXs="24" nzSm="8">
          <nz-form-item>
            <nz-form-label>Start Date To</nz-form-label>
            <nz-form-control>
              <nz-date-picker formControlName="endDate" nzPlaceHolder="Select date" style="width: 100%"></nz-date-picker>
            </nz-form-control>
          </nz-form-item>
        </div>
      </div>
      
      <div class="filter-actions">
        <button nz-button nzType="default" (click)="resetFilters()">
          <span nz-icon nzType="reload"></span>
          Reset
        </button>
      </div>
    </form>
  </nz-card>

  <!-- Bookings Table -->
  <nz-card class="bookings-card">
    <nz-table 
      #bookingsTable
      [nzData]="bookings"
      [nzLoading]="loading"
      [nzTotal]="total"
      [nzPageSize]="pageSize"
      [nzPageIndex]="pageIndex"
      [nzShowSizeChanger]="true"
      [nzShowQuickJumper]="true"
      [nzShowTotal]="totalTemplate"
      (nzPageIndexChange)="onPageChange($event)"
      (nzPageSizeChange)="onPageSizeChange($event)">
      
      <thead>
        <tr>
          <th>Contract Number</th>
          <th>Vehicle</th>
          <th>Rental Period</th>
          <th>Type</th>
          <th>Status</th>
          <th>Amount</th>
          <th>Actions</th>
        </tr>
      </thead>
      
      <tbody>
        <tr *ngFor="let booking of bookings">
          <td>
            <div class="contract-info">
              <div class="contract-number">{{ booking.contractNumber }}</div>
              <div class="contract-date">{{ booking.createdAt | date:'dd/MM/yyyy' }}</div>
            </div>
          </td>
          <td>
            <div class="vehicle-info">
              <div class="vehicle-name">{{ booking.vehicle?.vehicleType?.nameVehicleType }}</div>
              <div class="vehicle-plate">{{ booking.vehicle?.licensePlate }}</div>
            </div>
          </td>
          <td>
            <div class="rental-period">
              <div>{{ booking.startDate | date:'dd/MM/yyyy' }} - {{ booking.endDate | date:'dd/MM/yyyy' }}</div>
              <div class="duration">{{ getRentalDuration(booking) }} days</div>
            </div>
          </td>
          <td>
            <span class="rental-type">{{ booking.rentalType === 'SELF_DRIVE' ? 'Self Drive' : 'With Driver' }}</span>
          </td>
          <td>
            <nz-tag [nzColor]="booking.status | statusColor">
              <span nz-icon [nzType]="getStatusIcon(booking.status)"></span>
              {{ getBookingStatusText(booking.status) }}
            </nz-tag>
          </td>
          <td>
            <div class="amount">{{ booking.totalPrice | currencyFormat }}</div>
          </td>
          <td>
            <div class="actions">
              <button nz-button nzType="link" nzSize="small" (click)="viewBookingDetails(booking)">
                <span nz-icon nzType="eye"></span>
                View
              </button>
              
              <button 
                *ngIf="booking.status === 'COMPLETED'"
                nz-button 
                nzType="link" 
                nzSize="small" 
                (click)="downloadReceipt(booking)">
                <span nz-icon nzType="download"></span>
                Receipt
              </button>
              
              <button 
                *ngIf="canCancelBooking(booking)"
                nz-button 
                nzType="link" 
                nzSize="small" 
                nzDanger
                (click)="showCancelModal(booking)">
                <span nz-icon nzType="close"></span>
                Cancel
              </button>
            </div>
          </td>
        </tr>
      </tbody>
    </nz-table>
    
    <ng-template #totalTemplate let-total let-range="range">
      {{ range[0] }}-{{ range[1] }} of {{ total }} bookings
    </ng-template>

    <!-- Empty State -->
    <div *ngIf="!loading && bookings.length === 0" class="empty-state">
      <nz-empty 
        nzNotFoundImage="simple" 
        nzNotFoundContent="No bookings found">
        <ng-container nzNotFoundFooter>
          <button nz-button nzType="primary" routerLink="/customer/vehicles">
            Browse Vehicles
          </button>
        </ng-container>
      </nz-empty>
    </div>
  </nz-card>
</div>

<!-- Cancel Confirmation Modal -->
<app-confirmation-modal
  [(visible)]="cancelModalVisible"
  title="Cancel Booking"
  content="Are you sure you want to cancel this booking? This action cannot be undone."
  okText="Cancel Booking"
  okType="primary"
  [loading]="cancelLoading"
  (onOk)="confirmCancel()"
  (onCancel)="cancelCancel()">
</app-confirmation-modal>
