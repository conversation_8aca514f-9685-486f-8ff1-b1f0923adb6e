<app-page-header 
  title="Driver Management" 
  subtitle="Manage drivers and their availability status">
  
  <div nz-page-header-extra>
    <button nz-button nzType="primary" routerLink="/drivers/create">
      <span nz-icon nzType="plus"></span>
      Add Driver
    </button>
  </div>
</app-page-header>

<nz-card>
  <form nz-form [formGroup]="filterForm" class="filter-form">
    <div nz-row [nzGutter]="[16, 16]">
      <div nz-col nzXs="24" nzSm="12" nzMd="6">
        <nz-form-item>
          <nz-form-label>Search</nz-form-label>
          <nz-form-control>
            <input nz-input placeholder="Name, email, phone, license..." formControlName="search">
          </nz-form-control>
        </nz-form-item>
      </div>
      
      <div nz-col nzXs="24" nzSm="12" nzMd="6">
        <nz-form-item>
          <nz-form-label>Status</nz-form-label>
          <nz-form-control>
            <nz-select formControlName="status" nzPlaceHolder="Select status" nzAllowClear>
              <nz-option *ngFor="let status of driverStatusOptions" [nzValue]="status" [nzLabel]="status"></nz-option>
            </nz-select>
          </nz-form-control>
        </nz-form-item>
      </div>
      
      <div nz-col nzXs="24" nzSm="12" nzMd="6">
        <nz-form-item>
          <nz-form-label>Available From</nz-form-label>
          <nz-form-control>
            <nz-date-picker formControlName="availableFrom" nzPlaceHolder="Select date" style="width: 100%"></nz-date-picker>
          </nz-form-control>
        </nz-form-item>
      </div>
      
      <div nz-col nzXs="24" nzSm="12" nzMd="6">
        <nz-form-item>
          <nz-form-label>Available To</nz-form-label>
          <nz-form-control>
            <nz-date-picker formControlName="availableTo" nzPlaceHolder="Select date" style="width: 100%"></nz-date-picker>
          </nz-form-control>
        </nz-form-item>
      </div>
    </div>
    
    <div nz-row>
      <div nz-col nzSpan="24" class="filter-actions">
        <button nz-button nzType="default" (click)="resetFilters()">
          <span nz-icon nzType="reload"></span>
          Reset
        </button>
      </div>
    </div>
  </form>
</nz-card>

<nz-card style="margin-top: 16px;">
  <nz-table 
    #driverTable
    [nzData]="drivers"
    [nzLoading]="loading"
    [nzTotal]="total"
    [nzPageSize]="pageSize"
    [nzPageIndex]="pageIndex"
    [nzShowSizeChanger]="true"
    [nzShowQuickJumper]="true"
    [nzShowTotal]="totalTemplate"
    (nzPageIndexChange)="onPageChange($event)"
    (nzPageSizeChange)="onPageSizeChange($event)">
    
    <thead>
      <tr>
        <th>Driver Info</th>
        <th>Contact</th>
        <th>License Info</th>
        <th>License Status</th>
        <th>Status</th>
        <th>Actions</th>
      </tr>
    </thead>
    
    <tbody>
      <tr *ngFor="let driver of drivers">
        <td>
          <div><strong>{{ driver.fullName }}</strong></div>
          <div style="color: #666; font-size: 12px;">ID: {{ driver.idCardNumber }}</div>
        </td>
        <td>
          <div>{{ driver.email }}</div>
          <div style="color: #666;">{{ driver.phone }}</div>
        </td>
        <td>
          <div>{{ driver.driverLicenseNumber }}</div>
          <div style="font-size: 12px; color: #666;">
            Issue: {{ driver.licenseIssueDate | date:'dd/MM/yyyy' }}<br>
            Expires: {{ driver.licenseExpiryDate | date:'dd/MM/yyyy' }}
          </div>
        </td>
        <td>
          <nz-tag [nzColor]="isLicenseExpired(driver) ? 'red' : 'green'">
            {{ isLicenseExpired(driver) ? 'Expired' : 'Valid' }}
          </nz-tag>
        </td>
        <td>
          <nz-tag [nzColor]="driver.status | statusColor">
            {{ driver.status }}
          </nz-tag>
          <div style="margin-top: 4px;">
            <nz-select
              [ngModel]="driver.status"
              (ngModelChange)="updateDriverStatus(driver, $any($event))"
              nzSize="small"
              style="width: 120px;">
              <nz-option *ngFor="let status of driverStatusOptions" [nzValue]="status" [nzLabel]="status"></nz-option>
            </nz-select>
          </div>
        </td>
        <td>
          <button nz-button nzType="link" nzSize="small" [routerLink]="['/drivers/edit', driver.id]">
            <span nz-icon nzType="edit"></span>
            Edit
          </button>
          <nz-divider nzType="vertical"></nz-divider>
          <button nz-button nzType="link" nzSize="small" nzDanger (click)="showDeleteModal(driver)">
            <span nz-icon nzType="delete"></span>
            Delete
          </button>
        </td>
      </tr>
    </tbody>
  </nz-table>
  
  <ng-template #totalTemplate let-total let-range="range">
    {{ range[0] }}-{{ range[1] }} of {{ total }} drivers
  </ng-template>
</nz-card>

<app-confirmation-modal
  [(visible)]="deleteModalVisible"
  title="Delete Driver"
  content="Are you sure you want to delete this driver? This action cannot be undone."
  okText="Delete"
  okType="primary"
  [loading]="deleteLoading"
  (onOk)="confirmDelete()"
  (onCancel)="cancelDelete()">
</app-confirmation-modal>
