.profile-container {
  max-width: 800px;
  margin: 0 auto;
  padding: 20px;
  background: #f5f5f5;
  min-height: 100vh;
}

.page-header {
  text-align: center;
  margin-bottom: 32px;
  background: white;
  padding: 40px;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  
  h1 {
    font-size: 32px;
    font-weight: 600;
    color: #333;
    margin: 0 0 8px 0;
  }
  
  p {
    font-size: 16px;
    color: #666;
    margin: 0;
  }
}

.profile-tabs {
  ::ng-deep .ant-tabs-card > .ant-tabs-nav .ant-tabs-tab {
    border-radius: 8px 8px 0 0;
    
    &.ant-tabs-tab-active {
      background: white;
      border-bottom-color: white;
    }
  }
  
  ::ng-deep .ant-tabs-content-holder {
    background: white;
    border-radius: 0 0 8px 8px;
  }
}

.profile-card,
.security-card,
.account-info-card {
  border: none;
  box-shadow: none;
  
  ::ng-deep .ant-card-head {
    border-bottom: 1px solid #f0f0f0;
    
    .ant-card-head-title {
      font-size: 20px;
      font-weight: 600;
      color: #333;
    }
  }
  
  ::ng-deep .ant-card-body {
    padding: 32px;
  }
}

.license-section {
  margin-top: 32px;
  padding-top: 32px;
  border-top: 1px solid #f0f0f0;
  
  h3 {
    font-size: 18px;
    font-weight: 600;
    color: #333;
    margin-bottom: 24px;
  }
  
  .license-details {
    margin-top: 24px;
    padding: 24px;
    background: #f8f9fa;
    border-radius: 8px;
  }
}

.form-actions {
  margin-top: 32px;
  padding-top: 24px;
  border-top: 1px solid #f0f0f0;
  text-align: right;
  
  button {
    min-width: 150px;
  }
}

.account-details {
  .detail-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 16px 0;
    border-bottom: 1px solid #f0f0f0;
    
    &:last-child {
      border-bottom: none;
    }
    
    .label {
      font-weight: 500;
      color: #666;
    }
    
    .value {
      font-weight: 600;
      color: #333;
      
      &.active {
        color: #52c41a;
      }
    }
  }
}

nz-form-item {
  margin-bottom: 24px;
}

// Responsive design
@media (max-width: 768px) {
  .profile-container {
    padding: 10px;
  }
  
  .page-header {
    padding: 30px 20px;
    
    h1 {
      font-size: 24px;
    }
    
    p {
      font-size: 14px;
    }
  }
  
  .profile-card,
  .security-card,
  .account-info-card {
    ::ng-deep .ant-card-body {
      padding: 20px;
    }
  }
  
  .license-section {
    .license-details {
      padding: 16px;
    }
  }
  
  .form-actions {
    text-align: center;
    
    button {
      width: 100%;
    }
  }
  
  .account-details .detail-item {
    flex-direction: column;
    align-items: flex-start;
    gap: 4px;
  }
}

@media (max-width: 576px) {
  .page-header {
    padding: 20px 15px;
    
    h1 {
      font-size: 20px;
    }
  }
  
  .license-section h3 {
    font-size: 16px;
  }
}
