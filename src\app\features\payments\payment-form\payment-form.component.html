<app-page-header 
  [title]="isEditMode ? 'Edit Payment' : 'Add Payment'" 
  [subtitle]="isEditMode ? 'Update payment information' : 'Record a new payment'"
  [showBackButton]="true">
</app-page-header>

<nz-card [nzLoading]="loading">
  <form nz-form [formGroup]="paymentForm" (ngSubmit)="onSubmit()">
    <div nz-row [nzGutter]="[16, 16]">
      <div nz-col nzXs="24" nzSm="12">
        <nz-form-item>
          <nz-form-label [nzSpan]="24" nzRequired>Contract</nz-form-label>
          <nz-form-control [nzSpan]="24" nzErrorTip="Please select a contract">
            <nz-select 
              formControlName="contractId" 
              nzPlaceHolder="Select contract" 
              nzShowSearch
              (ngModelChange)="onContractChange($event)">
              <nz-option 
                *ngFor="let contract of contracts" 
                [nzValue]="contract.id" 
                [nzLabel]="contract.contractNumber + ' - ' + contract.customer?.fullName + ' (' + (contract.totalPrice | currency:'VND':'symbol':'1.0-0') + ')'">
              </nz-option>
            </nz-select>
          </nz-form-control>
        </nz-form-item>
      </div>

      <div nz-col nzXs="24" nzSm="12">
        <nz-form-item>
          <nz-form-label [nzSpan]="24" nzRequired>Amount (VND)</nz-form-label>
          <nz-form-control [nzSpan]="24" nzErrorTip="Please enter payment amount">
            <nz-input-number 
              formControlName="amount" 
              [nzMin]="0" 
              [nzStep]="100000"
              nzPlaceHolder="Enter payment amount"
              style="width: 100%">
            </nz-input-number>
          </nz-form-control>
        </nz-form-item>
      </div>

      <div nz-col nzXs="24" nzSm="12">
        <nz-form-item>
          <nz-form-label [nzSpan]="24" nzRequired>Payment Method</nz-form-label>
          <nz-form-control [nzSpan]="24" nzErrorTip="Please select payment method">
            <nz-select formControlName="paymentMethod" nzPlaceHolder="Select payment method">
              <nz-option
                *ngFor="let method of paymentMethodOptions"
                [nzValue]="method"
                [nzLabel]="method">
              </nz-option>
            </nz-select>
          </nz-form-control>
        </nz-form-item>
      </div>

      <div nz-col nzXs="24" nzSm="12" *ngIf="isEditMode">
        <nz-form-item>
          <nz-form-label [nzSpan]="24">Status</nz-form-label>
          <nz-form-control [nzSpan]="24">
            <nz-select formControlName="status" nzPlaceHolder="Select status">
              <nz-option *ngFor="let status of paymentStatusOptions" [nzValue]="status" [nzLabel]="status"></nz-option>
            </nz-select>
          </nz-form-control>
        </nz-form-item>
      </div>

      <div nz-col nzSpan="24">
        <nz-form-item>
          <nz-form-label [nzSpan]="24">Note</nz-form-label>
          <nz-form-control [nzSpan]="24">
            <textarea 
              nz-input 
              formControlName="note" 
              placeholder="Enter payment notes (optional)" 
              rows="3">
            </textarea>
          </nz-form-control>
        </nz-form-item>
      </div>
    </div>

    <div nz-row>
      <div nz-col nzSpan="24" class="form-actions">
        <button nz-button nzType="default" (click)="onCancel()" [disabled]="submitting">
          Cancel
        </button>
        <button 
          nz-button 
          nzType="primary" 
          [nzLoading]="submitting"
          [disabled]="!paymentForm.valid"
          style="margin-left: 8px;">
          {{ isEditMode ? 'Update' : 'Create' }}
        </button>
      </div>
    </div>
  </form>
</nz-card>
