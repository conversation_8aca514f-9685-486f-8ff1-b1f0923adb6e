export interface Driver {
  id: number;
  fullName: string;
  email: string;
  phone: string;
  address: string;
  idCardNumber: string;
  driverLicenseNumber: string;
  licenseIssueDate: Date;
  licenseExpiryDate: Date;
  status: DriverStatus;
  createdAt: Date;
  updatedAt: Date;
}

export enum DriverStatus {
  AVAILABLE = 'AVAILABLE',
  BUSY = 'BUSY',
  ON_LEAVE = 'ON_LEAVE',
  INACTIVE = 'INACTIVE'
}

export interface CreateDriverRequest {
  fullName: string;
  email: string;
  phone: string;
  address: string;
  idCardNumber: string;
  driverLicenseNumber: string;
  licenseIssueDate: Date;
  licenseExpiryDate: Date;
}

export interface UpdateDriverRequest {
  id: number;
  fullName: string;
  email: string;
  phone: string;
  address: string;
  idCardNumber: string;
  driverLicenseNumber: string;
  licenseIssueDate: Date;
  licenseExpiryDate: Date;
  status: DriverStatus;
}

import { BaseFilter } from './auth.model';

export interface DriverFilter extends BaseFilter {
  search?: string;
  status?: DriverStatus;
  availableFrom?: Date;
  availableTo?: Date;
}
