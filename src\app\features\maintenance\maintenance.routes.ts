import { Routes } from '@angular/router';

export const maintenanceRoutes: Routes = [
  {
    path: '',
    redirectTo: 'list',
    pathMatch: 'full'
  },
  {
    path: 'list',
    loadComponent: () => import('./maintenance-list/maintenance-list.component').then(m => m.MaintenanceListComponent)
  },
  {
    path: 'create',
    loadComponent: () => import('./maintenance-form/maintenance-form.component').then(m => m.MaintenanceFormComponent)
  },
  {
    path: 'edit/:id',
    loadComponent: () => import('./maintenance-form/maintenance-form.component').then(m => m.MaintenanceFormComponent)
  }
];
