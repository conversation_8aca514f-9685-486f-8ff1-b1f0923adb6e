export interface MaintenanceHistory {
  id: number;
  vehicleId: number;
  maintenanceDate: Date;
  description: string;
  cost: number;
  status: MaintenanceStatus;
  createdAt: Date;
  updatedAt: Date;
  vehicle?: {
    id: number;
    licensePlate: string;
    vehicleType: {
      nameVehicleType: string;
    };
  };
}

export enum MaintenanceStatus {
  SCHEDULED = 'SCHEDULED',
  IN_PROGRESS = 'IN_PROGRESS',
  COMPLETED = 'COMPLETED',
  CANCELLED = 'CANCELLED'
}

export interface CreateMaintenanceRequest {
  vehicleId: number;
  maintenanceDate: Date;
  description: string;
  cost: number;
}

export interface UpdateMaintenanceRequest {
  id: number;
  vehicleId: number;
  maintenanceDate: Date;
  description: string;
  cost: number;
  status: MaintenanceStatus;
}

import { BaseFilter } from './auth.model';

export interface MaintenanceFilter extends BaseFilter {
  vehicleId?: number;
  status?: MaintenanceStatus;
  startDate?: Date;
  endDate?: Date;
  minCost?: number;
  maxCost?: number;
}
