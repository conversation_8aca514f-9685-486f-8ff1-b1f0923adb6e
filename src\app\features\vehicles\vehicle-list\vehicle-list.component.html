<app-page-header 
  title="Vehicle Management" 
  subtitle="Manage your fleet of vehicles">
  
  <div nz-page-header-extra>
    <button nz-button nzType="primary" routerLink="/vehicles/create">
      <span nz-icon nzType="plus"></span>
      Add Vehicle
    </button>
  </div>
</app-page-header>

<nz-card>
  <form nz-form [formGroup]="filterForm" class="filter-form">
    <div nz-row [nzGutter]="[16, 16]">
      <div nz-col nzXs="24" nzSm="12" nzMd="6">
        <nz-form-item>
          <nz-form-label>Search</nz-form-label>
          <nz-form-control>
            <input nz-input placeholder="License plate, color..." formControlName="search">
          </nz-form-control>
        </nz-form-item>
      </div>
      
      <div nz-col nzXs="24" nzSm="12" nzMd="6">
        <nz-form-item>
          <nz-form-label>Vehicle Type</nz-form-label>
          <nz-form-control>
            <nz-select formControlName="vehicleTypeId" nzPlaceHolder="Select type" nzAllowClear>
              <nz-option *ngFor="let type of vehicleTypes" [nzValue]="type.id" [nzLabel]="type.nameVehicleType"></nz-option>
            </nz-select>
          </nz-form-control>
        </nz-form-item>
      </div>
      
      <div nz-col nzXs="24" nzSm="12" nzMd="6">
        <nz-form-item>
          <nz-form-label>Status</nz-form-label>
          <nz-form-control>
            <nz-select formControlName="status" nzPlaceHolder="Select status" nzAllowClear>
              <nz-option *ngFor="let status of vehicleStatusOptions" [nzValue]="status" [nzLabel]="status"></nz-option>
            </nz-select>
          </nz-form-control>
        </nz-form-item>
      </div>
      
      <div nz-col nzXs="24" nzSm="12" nzMd="6">
        <nz-form-item>
          <nz-form-label>Seats</nz-form-label>
          <nz-form-control>
            <nz-select formControlName="seatCount" nzPlaceHolder="Select seats" nzAllowClear>
              <nz-option nzValue="4" nzLabel="4 seats"></nz-option>
              <nz-option nzValue="5" nzLabel="5 seats"></nz-option>
              <nz-option nzValue="7" nzLabel="7 seats"></nz-option>
              <nz-option nzValue="16" nzLabel="16 seats"></nz-option>
            </nz-select>
          </nz-form-control>
        </nz-form-item>
      </div>
    </div>
    
    <div nz-row>
      <div nz-col nzSpan="24" class="filter-actions">
        <button nz-button nzType="default" (click)="resetFilters()">
          <span nz-icon nzType="reload"></span>
          Reset
        </button>
      </div>
    </div>
  </form>
</nz-card>

<nz-card style="margin-top: 16px;">
  <nz-table 
    #vehicleTable
    [nzData]="vehicles"
    [nzLoading]="loading"
    [nzTotal]="total"
    [nzPageSize]="pageSize"
    [nzPageIndex]="pageIndex"
    [nzShowSizeChanger]="true"
    [nzShowQuickJumper]="true"
    [nzShowTotal]="totalTemplate"
    (nzPageIndexChange)="onPageChange($event)"
    (nzPageSizeChange)="onPageSizeChange($event)">
    
    <thead>
      <tr>
        <th>License Plate</th>
        <th>Type</th>
        <th>Seats</th>
        <th>Color</th>
        <th>Status</th>
        <th>Base Price</th>
        <th>Actions</th>
      </tr>
    </thead>
    
    <tbody>
      <tr *ngFor="let vehicle of vehicles">
        <td>
          <strong>{{ vehicle.licensePlate }}</strong>
        </td>
        <td>{{ vehicle.vehicleType?.nameVehicleType || 'N/A' }}</td>
        <td>{{ vehicle.seatCount }}</td>
        <td>{{ vehicle.color }}</td>
        <td>
          <nz-tag [nzColor]="vehicle.status | statusColor">
            {{ vehicle.status }}
          </nz-tag>
        </td>
        <td>{{ vehicle.basePrice | currencyFormat }}</td>
        <td>
          <button nz-button nzType="link" nzSize="small" [routerLink]="['/vehicles/edit', vehicle.id]">
            <span nz-icon nzType="edit"></span>
            Edit
          </button>
          <nz-divider nzType="vertical"></nz-divider>
          <button nz-button nzType="link" nzSize="small" nzDanger (click)="showDeleteModal(vehicle)">
            <span nz-icon nzType="delete"></span>
            Delete
          </button>
        </td>
      </tr>
    </tbody>
  </nz-table>
  
  <ng-template #totalTemplate let-total let-range="range">
    {{ range[0] }}-{{ range[1] }} of {{ total }} vehicles
  </ng-template>
</nz-card>

<app-confirmation-modal
  [(visible)]="deleteModalVisible"
  title="Delete Vehicle"
  content="Are you sure you want to delete this vehicle? This action cannot be undone."
  okText="Delete"
  okType="primary"
  [loading]="deleteLoading"
  (onOk)="confirmDelete()"
  (onCancel)="cancelDelete()">
</app-confirmation-modal>
