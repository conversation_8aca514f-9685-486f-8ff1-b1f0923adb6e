import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { RouterModule } from '@angular/router';
import { ReactiveFormsModule, FormBuilder, FormGroup, Validators } from '@angular/forms';
import { NzCardModule } from 'ng-zorro-antd/card';
import { NzButtonModule } from 'ng-zorro-antd/button';
import { NzFormModule } from 'ng-zorro-antd/form';
import { NzInputModule } from 'ng-zorro-antd/input';
import { NzDatePickerModule } from 'ng-zorro-antd/date-picker';
import { NzCheckboxModule } from 'ng-zorro-antd/checkbox';
import { NzTabsModule } from 'ng-zorro-antd/tabs';
import { NzMessageService } from 'ng-zorro-antd/message';

import { CustomerAuthService } from '../../../core/services/customer-auth.service';
import { User } from '../../../core/models';
import { CustomValidators } from '../../../shared/validators/custom.validators';

@Component({
  selector: 'app-customer-profile',
  standalone: true,
  imports: [
    CommonModule,
    RouterModule,
    ReactiveFormsModule,
    NzCardModule,
    NzButtonModule,
    NzFormModule,
    NzInputModule,
    NzDatePickerModule,
    NzCheckboxModule,
    NzTabsModule
  ],
  templateUrl: './profile.component.html',
  styleUrls: ['./profile.component.scss']
})
export class CustomerProfileComponent implements OnInit {
  currentUser: User | null = null;
  profileForm!: FormGroup;
  passwordForm!: FormGroup;
  updating = false;
  changingPassword = false;

  constructor(
    private fb: FormBuilder,
    private customerAuthService: CustomerAuthService,
    private message: NzMessageService
  ) {}

  ngOnInit(): void {
    this.currentUser = this.customerAuthService.getCurrentCustomer();
    this.initForms();
    this.loadUserData();
  }

  private initForms(): void {
    this.profileForm = this.fb.group({
      fullName: ['', [Validators.required, Validators.minLength(2)]],
      email: ['', [Validators.required, Validators.email]],
      phone: ['', [Validators.required, CustomValidators.phoneNumber]],
      address: ['', [Validators.required]],
      idCardNumber: ['', [Validators.required, CustomValidators.idCardNumber]],
      hasDriverLicense: [false],
      driverLicenseNumber: [''],
      licenseIssueDate: [null],
      licenseExpiryDate: [null]
    });

    this.passwordForm = this.fb.group({
      currentPassword: ['', [Validators.required]],
      newPassword: ['', [Validators.required, Validators.minLength(6)]],
      confirmPassword: ['', [Validators.required]]
    });

    // Watch driver license checkbox
    this.profileForm.get('hasDriverLicense')?.valueChanges.subscribe(hasLicense => {
      const licenseNumberControl = this.profileForm.get('driverLicenseNumber');
      const issueControl = this.profileForm.get('licenseIssueDate');
      const expiryControl = this.profileForm.get('licenseExpiryDate');

      if (hasLicense) {
        licenseNumberControl?.setValidators([Validators.required, CustomValidators.driverLicenseNumber]);
        issueControl?.setValidators([Validators.required, CustomValidators.pastDate]);
        expiryControl?.setValidators([Validators.required, CustomValidators.futureDate]);
      } else {
        licenseNumberControl?.clearValidators();
        issueControl?.clearValidators();
        expiryControl?.clearValidators();
        licenseNumberControl?.setValue('');
        issueControl?.setValue(null);
        expiryControl?.setValue(null);
      }

      licenseNumberControl?.updateValueAndValidity();
      issueControl?.updateValueAndValidity();
      expiryControl?.updateValueAndValidity();
    });

    // Add password confirmation validator
    this.passwordForm.get('confirmPassword')?.setValidators([
      Validators.required,
      this.confirmPasswordValidator.bind(this)
    ]);
  }

  private confirmPasswordValidator(control: any) {
    const password = this.passwordForm?.get('newPassword')?.value;
    const confirmPassword = control.value;
    
    if (password !== confirmPassword) {
      return { confirmPassword: true };
    }
    return null;
  }

  private loadUserData(): void {
    if (this.currentUser) {
      const hasLicense = !!(this.currentUser.driverLicenseNumber);
      
      this.profileForm.patchValue({
        fullName: this.currentUser.fullName,
        email: this.currentUser.email,
        phone: this.currentUser.phone,
        address: this.currentUser.address,
        idCardNumber: this.currentUser.idCardNumber,
        hasDriverLicense: hasLicense,
        driverLicenseNumber: this.currentUser.driverLicenseNumber || '',
        licenseIssueDate: this.currentUser.licenseIssueDate ? new Date(this.currentUser.licenseIssueDate) : null,
        licenseExpiryDate: this.currentUser.licenseExpiryDate ? new Date(this.currentUser.licenseExpiryDate) : null
      });
    }
  }

  onUpdateProfile(): void {
    if (this.profileForm.valid) {
      this.updating = true;
      
      const formValue = this.profileForm.value;
      const profileData = {
        fullName: formValue.fullName,
        email: formValue.email,
        phone: formValue.phone,
        address: formValue.address,
        idCardNumber: formValue.idCardNumber,
        driverLicenseNumber: formValue.hasDriverLicense ? formValue.driverLicenseNumber : null,
        licenseIssueDate: formValue.hasDriverLicense ? formValue.licenseIssueDate : null,
        licenseExpiryDate: formValue.hasDriverLicense ? formValue.licenseExpiryDate : null
      };

      this.customerAuthService.updateProfile(profileData).subscribe({
        next: (updatedUser) => {
          this.currentUser = updatedUser;
          this.message.success('Profile updated successfully');
          this.updating = false;
        },
        error: (error) => {
          this.message.error(error.error?.message || 'Failed to update profile');
          this.updating = false;
        }
      });
    } else {
      this.markFormGroupTouched(this.profileForm);
    }
  }

  onChangePassword(): void {
    if (this.passwordForm.valid) {
      this.changingPassword = true;
      
      // This would typically call a change password API
      // For now, we'll simulate the API call
      setTimeout(() => {
        this.message.success('Password changed successfully');
        this.passwordForm.reset();
        this.changingPassword = false;
      }, 1000);
    } else {
      this.markFormGroupTouched(this.passwordForm);
    }
  }

  private markFormGroupTouched(formGroup: FormGroup): void {
    Object.keys(formGroup.controls).forEach(key => {
      const control = formGroup.get(key);
      if (control) {
        control.markAsTouched();
      }
    });
  }

  disabledDate = (current: Date): boolean => {
    const today = new Date();
    today.setHours(0, 0, 0, 0);
    return current && current > today;
  };

  disabledExpiryDate = (current: Date): boolean => {
    const today = new Date();
    today.setHours(0, 0, 0, 0);
    return current && current <= today;
  };
}
