.login-container {
  min-height: 100vh;
  display: flex;
  justify-content: center;
  align-items: center;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  padding: 20px;
}

.login-card {
  width: 100%;
  max-width: 400px;
}

.login-form-card {
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  border-radius: 12px;
  
  ::ng-deep .ant-card-head {
    text-align: center;
    border-bottom: 1px solid #f0f0f0;
    
    .ant-card-head-title {
      font-size: 28px;
      font-weight: 600;
      color: #1890ff;
    }
  }
  
  ::ng-deep .ant-card-body {
    padding: 32px;
  }
}

.subtitle {
  text-align: center;
  color: #666;
  margin-bottom: 32px;
  font-size: 16px;
}

.login-options {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.forgot-password {
  color: #1890ff;
  text-decoration: none;
  
  &:hover {
    text-decoration: underline;
  }
}

.login-button {
  width: 100%;
  height: 48px;
  font-size: 16px;
  font-weight: 500;
}

.password-toggle {
  cursor: pointer;
  color: #8c8c8c;
  
  &:hover {
    color: #1890ff;
  }
}

.divider {
  text-align: center;
  margin: 24px 0;
  position: relative;
  
  &::before {
    content: '';
    position: absolute;
    top: 50%;
    left: 0;
    right: 0;
    height: 1px;
    background: #f0f0f0;
  }
  
  span {
    background: white;
    padding: 0 16px;
    color: #999;
    position: relative;
    z-index: 1;
  }
}

.register-section {
  text-align: center;
  
  p {
    margin-bottom: 16px;
    color: #666;
  }
}

.register-button {
  width: 100%;
  height: 48px;
  font-size: 16px;
  font-weight: 500;
}

.back-to-home {
  text-align: center;
  margin-top: 24px;
  
  button {
    color: #666;
    
    &:hover {
      color: #1890ff;
    }
  }
}

nz-form-item {
  margin-bottom: 24px;
  
  &:last-child {
    margin-bottom: 0;
  }
}

::ng-deep .ant-input-affix-wrapper {
  .ant-input-prefix {
    color: #8c8c8c;
  }
}

// Responsive design
@media (max-width: 768px) {
  .login-container {
    padding: 10px;
  }
  
  .login-form-card {
    ::ng-deep .ant-card-body {
      padding: 20px;
    }
  }
}
