import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { RouterModule, ActivatedRoute, Router } from '@angular/router';
import { ReactiveFormsModule, FormsModule, FormBuilder, FormGroup, Validators } from '@angular/forms';
import { NzFormModule } from 'ng-zorro-antd/form';
import { NzInputModule } from 'ng-zorro-antd/input';
import { NzInputNumberModule } from 'ng-zorro-antd/input-number';
import { NzButtonModule } from 'ng-zorro-antd/button';
import { NzCardModule } from 'ng-zorro-antd/card';
import { NzSelectModule } from 'ng-zorro-antd/select';
import { NzDatePickerModule } from 'ng-zorro-antd/date-picker';
import { NzMessageService } from 'ng-zorro-antd/message';
import { PageHeaderComponent } from '../../../shared/components/page-header/page-header.component';
import { MaintenanceService, VehicleService } from '../../../core/services';
import { 
  MaintenanceHistory, 
  CreateMaintenanceRequest, 
  UpdateMaintenanceRequest, 
  MaintenanceStatus,
  Vehicle
} from '../../../core/models';
import { CustomValidators } from '../../../shared/validators/custom.validators';

@Component({
  selector: 'app-maintenance-form',
  standalone: true,
  imports: [
    CommonModule,
    RouterModule,
    ReactiveFormsModule,
    FormsModule,
    NzFormModule,
    NzInputModule,
    NzInputNumberModule,
    NzButtonModule,
    NzCardModule,
    NzSelectModule,
    NzDatePickerModule,
    PageHeaderComponent
  ],
  templateUrl: './maintenance-form.component.html',
  styleUrls: ['./maintenance-form.component.scss']
})
export class MaintenanceFormComponent implements OnInit {
  maintenanceForm!: FormGroup;
  isEditMode = false;
  maintenanceId: number | null = null;
  loading = false;
  submitting = false;

  vehicles: Vehicle[] = [];
  maintenanceStatusOptions = Object.values(MaintenanceStatus);

  constructor(
    private fb: FormBuilder,
    private route: ActivatedRoute,
    private router: Router,
    private maintenanceService: MaintenanceService,
    private vehicleService: VehicleService,
    private message: NzMessageService
  ) {}

  ngOnInit(): void {
    this.initForm();
    this.loadVehicles();
    this.checkEditMode();
  }

  private initForm(): void {
    this.maintenanceForm = this.fb.group({
      vehicleId: [null, [Validators.required]],
      maintenanceDate: [null, [Validators.required]],
      description: ['', [Validators.required, Validators.minLength(10)]],
      cost: [null, [Validators.required, CustomValidators.positiveNumber]],
      status: [MaintenanceStatus.SCHEDULED]
    });
  }

  private loadVehicles(): void {
    this.vehicleService.getVehicles({ limit: 1000 }).subscribe({
      next: (response) => {
        this.vehicles = response.data;
      },
      error: () => {
        this.message.error('Failed to load vehicles');
      }
    });
  }

  private checkEditMode(): void {
    const id = this.route.snapshot.paramMap.get('id');
    if (id) {
      this.isEditMode = true;
      this.maintenanceId = +id;
      this.loadMaintenance();
    }
  }

  private loadMaintenance(): void {
    if (this.maintenanceId) {
      this.loading = true;
      this.maintenanceService.getMaintenance(this.maintenanceId).subscribe({
        next: (maintenance) => {
          this.maintenanceForm.patchValue({
            vehicleId: maintenance.vehicleId,
            maintenanceDate: new Date(maintenance.maintenanceDate),
            description: maintenance.description,
            cost: maintenance.cost,
            status: maintenance.status
          });
          this.loading = false;
        },
        error: (error) => {
          this.message.error('Failed to load maintenance record');
          this.loading = false;
        }
      });
    }
  }

  onSubmit(): void {
    if (this.maintenanceForm.valid) {
      this.submitting = true;
      
      const formValue = this.maintenanceForm.value;
      const maintenanceData = {
        ...formValue,
        maintenanceDate: new Date(formValue.maintenanceDate)
      };
      
      if (this.isEditMode && this.maintenanceId) {
        const updateRequest: UpdateMaintenanceRequest = {
          id: this.maintenanceId,
          ...maintenanceData
        };
        
        this.maintenanceService.updateMaintenance(updateRequest).subscribe({
          next: () => {
            this.message.success('Maintenance record updated successfully');
            this.router.navigate(['/maintenance']);
          },
          error: (error) => {
            this.message.error('Failed to update maintenance record');
            this.submitting = false;
          }
        });
      } else {
        const createRequest: CreateMaintenanceRequest = maintenanceData;
        
        this.maintenanceService.createMaintenance(createRequest).subscribe({
          next: () => {
            this.message.success('Maintenance record created successfully');
            this.router.navigate(['/maintenance']);
          },
          error: (error) => {
            this.message.error('Failed to create maintenance record');
            this.submitting = false;
          }
        });
      }
    } else {
      this.markFormGroupTouched();
    }
  }

  private markFormGroupTouched(): void {
    Object.keys(this.maintenanceForm.controls).forEach(key => {
      const control = this.maintenanceForm.get(key);
      if (control) {
        control.markAsTouched();
      }
    });
  }

  onCancel(): void {
    this.router.navigate(['/maintenance']);
  }

  getMaintenanceTypeOptions(): string[] {
    return [
      'Oil Change',
      'Tire Replacement',
      'Brake Service',
      'Engine Repair',
      'Transmission Service',
      'Air Conditioning Service',
      'Battery Replacement',
      'General Inspection',
      'Body Repair',
      'Interior Cleaning',
      'Other'
    ];
  }

  onMaintenanceTypeSelect(type: string): void {
    const currentDescription = this.maintenanceForm.get('description')?.value || '';
    if (!currentDescription.includes(type)) {
      this.maintenanceForm.get('description')?.setValue(
        currentDescription ? `${currentDescription}, ${type}` : type
      );
    }
  }
}
