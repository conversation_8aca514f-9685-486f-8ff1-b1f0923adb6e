import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { RouterModule } from '@angular/router';
import { ReactiveFormsModule, FormBuilder, FormGroup } from '@angular/forms';
import { NzTableModule } from 'ng-zorro-antd/table';
import { NzButtonModule } from 'ng-zorro-antd/button';
import { NzInputModule } from 'ng-zorro-antd/input';
import { NzSelectModule } from 'ng-zorro-antd/select';
import { NzTagModule } from 'ng-zorro-antd/tag';
import { NzCardModule } from 'ng-zorro-antd/card';
import { NzFormModule } from 'ng-zorro-antd/form';
import { NzIconModule } from 'ng-zorro-antd/icon';
import { NzDividerModule } from 'ng-zorro-antd/divider';
import { NzDatePickerModule } from 'ng-zorro-antd/date-picker';
import { NzMessageService } from 'ng-zorro-antd/message';
import { Observable, BehaviorSubject } from 'rxjs';
import { debounceTime, distinctUntilChanged, switchMap } from 'rxjs/operators';

import { PageHeaderComponent } from '../../../shared/components/page-header/page-header.component';
import { ConfirmationModalComponent } from '../../../shared/components/confirmation-modal/confirmation-modal.component';
import { StatusColorPipe } from '../../../shared/pipes/status-color.pipe';
import { CurrencyFormatPipe } from '../../../shared/pipes/currency-format.pipe';
import { ContractService } from '../../../core/services';
import { Contract, ContractFilter, ContractStatus, RentalType, PaginatedResponse } from '../../../core/models';

@Component({
  selector: 'app-contract-list',
  standalone: true,
  imports: [
    CommonModule,
    RouterModule,
    ReactiveFormsModule,
    NzTableModule,
    NzButtonModule,
    NzInputModule,
    NzSelectModule,
    NzTagModule,
    NzCardModule,
    NzFormModule,
    NzIconModule,
    NzDividerModule,
    NzDatePickerModule,
    PageHeaderComponent,
    ConfirmationModalComponent,
    StatusColorPipe,
    CurrencyFormatPipe
  ],
  templateUrl: './contract-list.component.html',
  styleUrls: ['./contract-list.component.scss']
})
export class ContractListComponent implements OnInit {
  contracts: Contract[] = [];
  loading = false;
  total = 0;
  pageSize = 10;
  pageIndex = 1;
  
  filterForm!: FormGroup;
  private filterSubject = new BehaviorSubject<ContractFilter>({});
  
  deleteModalVisible = false;
  deleteLoading = false;
  contractToDelete: Contract | null = null;
  
  contractStatusOptions = Object.values(ContractStatus);
  rentalTypeOptions = Object.values(RentalType);

  constructor(
    private fb: FormBuilder,
    private contractService: ContractService,
    private message: NzMessageService
  ) {}

  ngOnInit(): void {
    this.initFilterForm();
    this.setupFilterSubscription();
    this.loadContracts();
  }

  private initFilterForm(): void {
    this.filterForm = this.fb.group({
      search: [''],
      status: [null],
      rentalType: [null],
      startDate: [null],
      endDate: [null]
    });
  }

  private setupFilterSubscription(): void {
    this.filterForm.valueChanges.pipe(
      debounceTime(300),
      distinctUntilChanged()
    ).subscribe(values => {
      const filter: ContractFilter = {
        ...values,
        page: 1,
        limit: this.pageSize
      };
      this.filterSubject.next(filter);
      this.pageIndex = 1;
    });

    this.filterSubject.pipe(
      switchMap(filter => {
        this.loading = true;
        return this.contractService.getContracts(filter);
      })
    ).subscribe({
      next: (response: PaginatedResponse<Contract>) => {
        this.contracts = response.data;
        this.total = response.total;
        this.loading = false;
      },
      error: (error) => {
        this.message.error('Failed to load contracts');
        this.loading = false;
      }
    });
  }

  private loadContracts(): void {
    const filter: ContractFilter = {
      ...this.filterForm.value,
      page: this.pageIndex,
      limit: this.pageSize
    };
    this.filterSubject.next(filter);
  }

  onPageChange(pageIndex: number): void {
    this.pageIndex = pageIndex;
    this.loadContracts();
  }

  onPageSizeChange(pageSize: number): void {
    this.pageSize = pageSize;
    this.pageIndex = 1;
    this.loadContracts();
  }

  resetFilters(): void {
    this.filterForm.reset();
  }

  showDeleteModal(contract: Contract): void {
    this.contractToDelete = contract;
    this.deleteModalVisible = true;
  }

  confirmDelete(): void {
    if (this.contractToDelete) {
      this.deleteLoading = true;
      this.contractService.deleteContract(this.contractToDelete.id).subscribe({
        next: () => {
          this.message.success('Contract deleted successfully');
          this.deleteModalVisible = false;
          this.deleteLoading = false;
          this.contractToDelete = null;
          this.loadContracts();
        },
        error: (error) => {
          this.message.error('Failed to delete contract');
          this.deleteLoading = false;
        }
      });
    }
  }

  cancelDelete(): void {
    this.deleteModalVisible = false;
    this.contractToDelete = null;
  }

  generatePdf(contract: Contract): void {
    this.contractService.generateContractPdf(contract.id).subscribe({
      next: (blob) => {
        const url = window.URL.createObjectURL(blob);
        const link = document.createElement('a');
        link.href = url;
        link.download = `contract-${contract.contractNumber}.pdf`;
        link.click();
        window.URL.revokeObjectURL(url);
      },
      error: (error) => {
        this.message.error('Failed to generate PDF');
      }
    });
  }

  getRentalDuration(contract: Contract): number {
    const start = new Date(contract.startDate);
    const end = new Date(contract.endDate);
    return Math.ceil((end.getTime() - start.getTime()) / (1000 * 3600 * 24));
  }
}
