import { Routes } from '@angular/router';

export const driverRoutes: Routes = [
  {
    path: '',
    redirectTo: 'list',
    pathMatch: 'full'
  },
  {
    path: 'list',
    loadComponent: () => import('./driver-list/driver-list.component').then(m => m.DriverListComponent)
  },
  {
    path: 'create',
    loadComponent: () => import('./driver-form/driver-form.component').then(m => m.DriverFormComponent)
  },
  {
    path: 'edit/:id',
    loadComponent: () => import('./driver-form/driver-form.component').then(m => m.DriverFormComponent)
  }
];
