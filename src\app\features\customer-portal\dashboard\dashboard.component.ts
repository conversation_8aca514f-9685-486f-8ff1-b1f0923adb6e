import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { RouterModule } from '@angular/router';
import { NzCardModule } from 'ng-zorro-antd/card';
import { NzStatisticModule } from 'ng-zorro-antd/statistic';
import { NzButtonModule } from 'ng-zorro-antd/button';
import { NzIconModule } from 'ng-zorro-antd/icon';
import { NzTagModule } from 'ng-zorro-antd/tag';
import { NzTableModule } from 'ng-zorro-antd/table';
import { NzEmptyModule } from 'ng-zorro-antd/empty';
import { NzSpinModule } from 'ng-zorro-antd/spin';
import { NzGridModule } from 'ng-zorro-antd/grid';
import { NzMessageService } from 'ng-zorro-antd/message';

import { CustomerAuthService } from '../../../core/services/customer-auth.service';
import { ContractService } from '../../../core/services';
import { User, Contract } from '../../../core/models';
import { StatusColorPipe } from '../../../shared/pipes/status-color.pipe';
import { CurrencyFormatPipe } from '../../../shared/pipes/currency-format.pipe';

@Component({
  selector: 'app-customer-dashboard',
  standalone: true,
  imports: [
    CommonModule,
    RouterModule,
    NzCardModule,
    NzStatisticModule,
    NzButtonModule,
    NzIconModule,
    NzTagModule,
    NzTableModule,
    NzEmptyModule,
    NzSpinModule,
    NzGridModule,
    StatusColorPipe,
    CurrencyFormatPipe
  ],
  templateUrl: './dashboard.component.html',
  styleUrls: ['./dashboard.component.scss']
})
export class CustomerDashboardComponent implements OnInit {
  currentUser: User | null = null;
  recentBookings: Contract[] = [];
  loading = false;
  
  stats = {
    totalBookings: 0,
    activeBookings: 0,
    completedBookings: 0,
    totalSpent: 0
  };

  constructor(
    private customerAuthService: CustomerAuthService,
    private contractService: ContractService,
    private message: NzMessageService
  ) {}

  ngOnInit(): void {
    this.currentUser = this.customerAuthService.getCurrentCustomer();
    this.loadDashboardData();
  }

  private loadDashboardData(): void {
    if (!this.currentUser) return;

    this.loading = true;
    
    // Load customer's contracts
    this.contractService.getContracts({ 
      customerId: this.currentUser.id,
      limit: 5,
      page: 1
    }).subscribe({
      next: (response) => {
        this.recentBookings = response.data;
        this.calculateStats(response.data);
        this.loading = false;
      },
      error: (error) => {
        this.message.error('Failed to load dashboard data');
        this.loading = false;
      }
    });
  }

  private calculateStats(contracts: Contract[]): void {
    this.stats.totalBookings = contracts.length;
    this.stats.activeBookings = contracts.filter(c => 
      c.status === 'ACTIVE' || c.status === 'CONFIRMED'
    ).length;
    this.stats.completedBookings = contracts.filter(c => 
      c.status === 'COMPLETED'
    ).length;
    this.stats.totalSpent = contracts
      .filter(c => c.status === 'COMPLETED')
      .reduce((sum, c) => sum + c.totalPrice, 0);
  }

  getWelcomeMessage(): string {
    const hour = new Date().getHours();
    if (hour < 12) return 'Good Morning';
    if (hour < 18) return 'Good Afternoon';
    return 'Good Evening';
  }

  getBookingStatusText(status: string): string {
    const statusMap: { [key: string]: string } = {
      'PENDING': 'Pending Approval',
      'CONFIRMED': 'Confirmed',
      'ACTIVE': 'Active Rental',
      'COMPLETED': 'Completed',
      'CANCELLED': 'Cancelled'
    };
    return statusMap[status] || status;
  }

  getRentalDuration(contract: Contract): number {
    const start = new Date(contract.startDate);
    const end = new Date(contract.endDate);
    return Math.ceil((end.getTime() - start.getTime()) / (1000 * 3600 * 24));
  }

  canCancelBooking(contract: Contract): boolean {
    const now = new Date();
    const startDate = new Date(contract.startDate);
    const hoursUntilStart = (startDate.getTime() - now.getTime()) / (1000 * 3600);
    
    return contract.status === 'PENDING' || 
           (contract.status === 'CONFIRMED' && hoursUntilStart > 24);
  }

  viewAllBookings(): void {
    // Navigate to bookings page
  }

  browseVehicles(): void {
    // Navigate to vehicle browse page
  }
}
