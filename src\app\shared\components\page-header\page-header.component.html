<nz-page-header 
  [nzTitle]="title" 
  [nzSubtitle]="subtitle"
  [nzBackIcon]="showBackButton ? 'arrow-left' : null">
  
  <nz-breadcrumb nz-page-header-breadcrumb *ngIf="breadcrumbs.length > 0">
    <nz-breadcrumb-item *ngFor="let item of breadcrumbs">
      <a *ngIf="item.url; else noLink" [routerLink]="item.url">{{ item.label }}</a>
      <ng-template #noLink>{{ item.label }}</ng-template>
    </nz-breadcrumb-item>
  </nz-breadcrumb>

  <ng-content nz-page-header-extra></ng-content>
  <ng-content nz-page-header-content></ng-content>
</nz-page-header>
