import { Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { Observable } from 'rxjs';
import { map } from 'rxjs/operators';
import { BaseHttpService } from './base-http.service';
import { 
  Contract, 
  CreateContractRequest, 
  UpdateContractRequest,
  ContractFilter,
  ContractStatus,
  ApiResponse,
  PaginatedResponse
} from '../models';
import { environment } from '../../../environments/environment';

@Injectable({
  providedIn: 'root'
})
export class ContractService extends BaseHttpService {
  protected baseUrl = environment.contract_service;

  constructor(httpClient: HttpClient) {
    super(httpClient);
  }

  getContracts(filter?: ContractFilter): Observable<PaginatedResponse<Contract>> {
    return this.getPaginated<Contract>('/contracts', filter).pipe(
      map(response => response.data)
    );
  }

  getContract(id: number): Observable<Contract> {
    return this.get<Contract>(`/contracts/${id}`).pipe(
      map(response => response.data)
    );
  }

  createContract(contract: CreateContractRequest): Observable<Contract> {
    return this.post<Contract>('/contracts', contract).pipe(
      map(response => response.data)
    );
  }

  updateContract(contract: UpdateContractRequest): Observable<Contract> {
    return this.put<Contract>(`/contracts/${contract.id}`, contract).pipe(
      map(response => response.data)
    );
  }

  updateContractStatus(id: number, status: ContractStatus): Observable<Contract> {
    return this.put<Contract>(`/contracts/${id}/status`, { status }).pipe(
      map(response => response.data)
    );
  }

  deleteContract(id: number): Observable<boolean> {
    return this.delete<any>(`/contracts/${id}`).pipe(
      map(response => response.success)
    );
  }

  generateContractPdf(id: number): Observable<Blob> {
    return this.downloadFile(`/contracts/${id}/pdf`);
  }

  getActiveContracts(): Observable<Contract[]> {
    return this.get<Contract[]>('/contracts/active').pipe(
      map(response => response.data)
    );
  }

  getContractsByCustomer(customerId: number): Observable<Contract[]> {
    return this.get<Contract[]>(`/contracts/customer/${customerId}`).pipe(
      map(response => response.data)
    );
  }

  getContractsByVehicle(vehicleId: number): Observable<Contract[]> {
    return this.get<Contract[]>(`/contracts/vehicle/${vehicleId}`).pipe(
      map(response => response.data)
    );
  }

  getContractsByDriver(driverId: number): Observable<Contract[]> {
    return this.get<Contract[]>(`/contracts/driver/${driverId}`).pipe(
      map(response => response.data)
    );
  }
}
