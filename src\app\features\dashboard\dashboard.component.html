<app-page-header 
  title="Dashboard" 
  subtitle="Overview of car rental operations">
</app-page-header>

<div nz-row [nzGutter]="[16, 16]">
  <div nz-col nzXs="24" nzSm="12" nzMd="8" nzLg="6">
    <nz-card>
      <nz-statistic
        nzTitle="Total Vehicles"
        [nzValue]="stats.totalVehicles"
        [nzPrefix]="iconTemplate"
        [nzValueStyle]="{ color: '#3f8600' }">
      </nz-statistic>
      <ng-template #iconTemplate>
        <span nz-icon nzType="car" style="color: #3f8600;"></span>
      </ng-template>
    </nz-card>
  </div>

  <div nz-col nzXs="24" nzSm="12" nzMd="8" nzLg="6">
    <nz-card>
      <nz-statistic
        nzTitle="Available Vehicles"
        [nzValue]="stats.availableVehicles"
        [nzPrefix]="availableIconTemplate"
        [nzValueStyle]="{ color: '#1890ff' }">
      </nz-statistic>
      <ng-template #availableIconTemplate>
        <span nz-icon nzType="check-circle" style="color: #1890ff;"></span>
      </ng-template>
    </nz-card>
  </div>

  <div nz-col nzXs="24" nzSm="12" nzMd="8" nzLg="6">
    <nz-card>
      <nz-statistic
        nzTitle="Active Contracts"
        [nzValue]="stats.activeContracts"
        [nzPrefix]="contractIconTemplate"
        [nzValueStyle]="{ color: '#722ed1' }">
      </nz-statistic>
      <ng-template #contractIconTemplate>
        <span nz-icon nzType="file-text" style="color: #722ed1;"></span>
      </ng-template>
    </nz-card>
  </div>

  <div nz-col nzXs="24" nzSm="12" nzMd="8" nzLg="6">
    <nz-card>
      <nz-statistic
        nzTitle="Total Revenue"
        [nzValue]="stats.totalRevenue"
        [nzPrefix]="revenueIconTemplate"
        [nzValueStyle]="{ color: '#52c41a' }"
        nzSuffix="VND">
      </nz-statistic>
      <ng-template #revenueIconTemplate>
        <span nz-icon nzType="dollar" style="color: #52c41a;"></span>
      </ng-template>
    </nz-card>
  </div>

  <div nz-col nzXs="24" nzSm="12" nzMd="8" nzLg="6">
    <nz-card>
      <nz-statistic
        nzTitle="Pending Payments"
        [nzValue]="stats.pendingPayments"
        [nzPrefix]="paymentIconTemplate"
        [nzValueStyle]="{ color: '#fa8c16' }">
      </nz-statistic>
      <ng-template #paymentIconTemplate>
        <span nz-icon nzType="clock-circle" style="color: #fa8c16;"></span>
      </ng-template>
    </nz-card>
  </div>

  <div nz-col nzXs="24" nzSm="12" nzMd="8" nzLg="6">
    <nz-card>
      <nz-statistic
        nzTitle="Maintenance Scheduled"
        [nzValue]="stats.maintenanceScheduled"
        [nzPrefix]="maintenanceIconTemplate"
        [nzValueStyle]="{ color: '#f5222d' }">
      </nz-statistic>
      <ng-template #maintenanceIconTemplate>
        <span nz-icon nzType="tool" style="color: #f5222d;"></span>
      </ng-template>
    </nz-card>
  </div>
</div>

<div nz-row [nzGutter]="[16, 16]" style="margin-top: 24px;">
  <div nz-col nzSpan="24">
    <nz-card nzTitle="Quick Actions">
      <p>Recent activity and quick action buttons will be implemented here.</p>
    </nz-card>
  </div>
</div>
