import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { RouterModule } from '@angular/router';
import { ReactiveFormsModule, FormBuilder, FormGroup } from '@angular/forms';
import { NzTableModule } from 'ng-zorro-antd/table';
import { NzButtonModule } from 'ng-zorro-antd/button';
import { NzInputModule } from 'ng-zorro-antd/input';
import { NzSelectModule } from 'ng-zorro-antd/select';
import { NzTagModule } from 'ng-zorro-antd/tag';
import { NzCardModule } from 'ng-zorro-antd/card';
import { NzFormModule } from 'ng-zorro-antd/form';
import { NzIconModule } from 'ng-zorro-antd/icon';
import { NzDividerModule } from 'ng-zorro-antd/divider';
import { NzDatePickerModule } from 'ng-zorro-antd/date-picker';
import { NzInputNumberModule } from 'ng-zorro-antd/input-number';
import { NzMessageService } from 'ng-zorro-antd/message';
import { Observable, BehaviorSubject } from 'rxjs';
import { debounceTime, distinctUntilChanged, switchMap } from 'rxjs/operators';

import { PageHeaderComponent } from '../../../shared/components/page-header/page-header.component';
import { ConfirmationModalComponent } from '../../../shared/components/confirmation-modal/confirmation-modal.component';
import { StatusColorPipe } from '../../../shared/pipes/status-color.pipe';
import { CurrencyFormatPipe } from '../../../shared/pipes/currency-format.pipe';
import { MaintenanceService, VehicleService } from '../../../core/services';
import { 
  MaintenanceHistory, 
  MaintenanceFilter, 
  MaintenanceStatus, 
  Vehicle,
  PaginatedResponse 
} from '../../../core/models';

@Component({
  selector: 'app-maintenance-list',
  standalone: true,
  imports: [
    CommonModule,
    RouterModule,
    ReactiveFormsModule,
    NzTableModule,
    NzButtonModule,
    NzInputModule,
    NzSelectModule,
    NzTagModule,
    NzCardModule,
    NzFormModule,
    NzIconModule,
    NzDividerModule,
    NzDatePickerModule,
    NzInputNumberModule,
    PageHeaderComponent,
    ConfirmationModalComponent,
    StatusColorPipe,
    CurrencyFormatPipe
  ],
  templateUrl: './maintenance-list.component.html',
  styleUrls: ['./maintenance-list.component.scss']
})
export class MaintenanceListComponent implements OnInit {
  maintenanceRecords: MaintenanceHistory[] = [];
  vehicles: Vehicle[] = [];
  loading = false;
  total = 0;
  pageSize = 10;
  pageIndex = 1;
  
  filterForm!: FormGroup;
  private filterSubject = new BehaviorSubject<MaintenanceFilter>({});
  
  deleteModalVisible = false;
  deleteLoading = false;
  maintenanceToDelete: MaintenanceHistory | null = null;
  
  maintenanceStatusOptions = Object.values(MaintenanceStatus);

  constructor(
    private fb: FormBuilder,
    private maintenanceService: MaintenanceService,
    private vehicleService: VehicleService,
    private message: NzMessageService
  ) {}

  ngOnInit(): void {
    this.initFilterForm();
    this.loadVehicles();
    this.setupFilterSubscription();
    this.loadMaintenanceRecords();
  }

  private initFilterForm(): void {
    this.filterForm = this.fb.group({
      vehicleId: [null],
      status: [null],
      startDate: [null],
      endDate: [null],
      minCost: [null],
      maxCost: [null]
    });
  }

  private loadVehicles(): void {
    this.vehicleService.getVehicles({ limit: 1000 }).subscribe({
      next: (response) => {
        this.vehicles = response.data;
      },
      error: () => {
        this.message.error('Failed to load vehicles');
      }
    });
  }

  private setupFilterSubscription(): void {
    this.filterForm.valueChanges.pipe(
      debounceTime(300),
      distinctUntilChanged()
    ).subscribe(values => {
      const filter: MaintenanceFilter = {
        ...values,
        page: 1,
        limit: this.pageSize
      };
      this.filterSubject.next(filter);
      this.pageIndex = 1;
    });

    this.filterSubject.pipe(
      switchMap(filter => {
        this.loading = true;
        return this.maintenanceService.getMaintenanceHistory(filter);
      })
    ).subscribe({
      next: (response: PaginatedResponse<MaintenanceHistory>) => {
        this.maintenanceRecords = response.data;
        this.total = response.total;
        this.loading = false;
      },
      error: (error) => {
        this.message.error('Failed to load maintenance records');
        this.loading = false;
      }
    });
  }

  private loadMaintenanceRecords(): void {
    const filter: MaintenanceFilter = {
      ...this.filterForm.value,
      page: this.pageIndex,
      limit: this.pageSize
    };
    this.filterSubject.next(filter);
  }

  onPageChange(pageIndex: number): void {
    this.pageIndex = pageIndex;
    this.loadMaintenanceRecords();
  }

  onPageSizeChange(pageSize: number): void {
    this.pageSize = pageSize;
    this.pageIndex = 1;
    this.loadMaintenanceRecords();
  }

  resetFilters(): void {
    this.filterForm.reset();
  }

  showDeleteModal(maintenance: MaintenanceHistory): void {
    this.maintenanceToDelete = maintenance;
    this.deleteModalVisible = true;
  }

  confirmDelete(): void {
    if (this.maintenanceToDelete) {
      this.deleteLoading = true;
      this.maintenanceService.deleteMaintenance(this.maintenanceToDelete.id).subscribe({
        next: () => {
          this.message.success('Maintenance record deleted successfully');
          this.deleteModalVisible = false;
          this.deleteLoading = false;
          this.maintenanceToDelete = null;
          this.loadMaintenanceRecords();
        },
        error: (error) => {
          this.message.error('Failed to delete maintenance record');
          this.deleteLoading = false;
        }
      });
    }
  }

  cancelDelete(): void {
    this.deleteModalVisible = false;
    this.maintenanceToDelete = null;
  }

  getStatusIcon(status: MaintenanceStatus): string {
    const iconMap: { [key in MaintenanceStatus]: string } = {
      [MaintenanceStatus.SCHEDULED]: 'clock-circle',
      [MaintenanceStatus.IN_PROGRESS]: 'loading',
      [MaintenanceStatus.COMPLETED]: 'check-circle',
      [MaintenanceStatus.CANCELLED]: 'close-circle'
    };
    return iconMap[status] || 'clock-circle';
  }

  isOverdue(maintenance: MaintenanceHistory): boolean {
    if (maintenance.status === MaintenanceStatus.COMPLETED || 
        maintenance.status === MaintenanceStatus.CANCELLED) {
      return false;
    }
    return new Date(maintenance.maintenanceDate) < new Date();
  }
}
