import { Pipe, PipeTransform } from '@angular/core';

@Pipe({
  name: 'statusColor',
  standalone: true
})
export class StatusColorPipe implements PipeTransform {
  transform(status: string): string {
    const statusColorMap: { [key: string]: string } = {
      // Vehicle Status
      'AVAILABLE': 'green',
      'RENTED': 'blue',
      'MAINTENANCE': 'orange',
      'OUT_OF_SERVICE': 'red',

      // Driver Status
      'BUSY': 'blue',
      'ON_LEAVE': 'orange',
      'INACTIVE': 'red',

      // Contract Status
      'PENDING': 'orange',
      'CONFIRMED': 'blue',
      'ACTIVE': 'green',
      'CONTRACT_COMPLETED': 'default',
      'CONTRACT_CANCELLED': 'red',

      // Payment Status
      'PAYMENT_PENDING': 'orange',
      'PAYMENT_COMPLETED': 'green',
      'FAILED': 'red',
      'REFUNDED': 'orange',

      // Maintenance Status
      'SCHEDULED': 'blue',
      'IN_PROGRESS': 'orange',
      'MAINTENANCE_COMPLETED': 'green',
      'MAINTENANCE_CANCELLED': 'red'
    };

    return statusColorMap[status] || 'default';
  }
}
