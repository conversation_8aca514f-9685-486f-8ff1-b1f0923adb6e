import { Component, Input, Output, EventEmitter } from '@angular/core';
import { CommonModule } from '@angular/common';
import { NzModalModule } from 'ng-zorro-antd/modal';
import { NzButtonModule } from 'ng-zorro-antd/button';
import { NzIconModule } from 'ng-zorro-antd/icon';

@Component({
  selector: 'app-confirmation-modal',
  standalone: true,
  imports: [CommonModule, NzModalModule, NzButtonModule, NzIconModule],
  templateUrl: './confirmation-modal.component.html',
  styleUrls: ['./confirmation-modal.component.scss']
})
export class ConfirmationModalComponent {
  @Input() visible = false;
  @Input() title = 'Confirm Action';
  @Input() content = 'Are you sure you want to proceed?';
  @Input() okText = 'OK';
  @Input() cancelText = 'Cancel';
  @Input() okType: 'primary' | 'default' | 'dashed' | 'text' | 'link' = 'primary';
  @Input() loading = false;

  @Output() visibleChange = new EventEmitter<boolean>();
  @Output() onOk = new EventEmitter<void>();
  @Output() onCancel = new EventEmitter<void>();

  handleOk(): void {
    this.onOk.emit();
  }

  handleCancel(): void {
    this.visible = false;
    this.visibleChange.emit(false);
    this.onCancel.emit();
  }
}
