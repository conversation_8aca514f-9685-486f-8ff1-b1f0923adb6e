import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { RouterModule, ActivatedRoute, Router } from '@angular/router';
import { ReactiveFormsModule, FormBuilder, FormGroup, Validators } from '@angular/forms';
import { NzFormModule } from 'ng-zorro-antd/form';
import { NzInputModule } from 'ng-zorro-antd/input';
import { NzInputNumberModule } from 'ng-zorro-antd/input-number';
import { NzSelectModule } from 'ng-zorro-antd/select';
import { NzButtonModule } from 'ng-zorro-antd/button';
import { NzCardModule } from 'ng-zorro-antd/card';
import { NzMessageService } from 'ng-zorro-antd/message';
import { PageHeaderComponent } from '../../../shared/components/page-header/page-header.component';
import { VehicleService, VehicleTypeService } from '../../../core/services';
import { Vehicle, VehicleType, CreateVehicleRequest, UpdateVehicleRequest, VehicleStatus } from '../../../core/models';

@Component({
  selector: 'app-vehicle-form',
  standalone: true,
  imports: [
    CommonModule,
    RouterModule,
    ReactiveFormsModule,
    NzFormModule,
    NzInputModule,
    NzInputNumberModule,
    NzSelectModule,
    NzButtonModule,
    NzCardModule,
    PageHeaderComponent
  ],
  templateUrl: './vehicle-form.component.html',
  styleUrls: ['./vehicle-form.component.scss']
})
export class VehicleFormComponent implements OnInit {
  vehicleForm!: FormGroup;
  vehicleTypes: VehicleType[] = [];
  isEditMode = false;
  vehicleId: number | null = null;
  loading = false;
  submitting = false;

  vehicleStatusOptions = Object.values(VehicleStatus);

  constructor(
    private fb: FormBuilder,
    private route: ActivatedRoute,
    private router: Router,
    private vehicleService: VehicleService,
    private vehicleTypeService: VehicleTypeService,
    private message: NzMessageService
  ) {}

  ngOnInit(): void {
    this.initForm();
    this.loadVehicleTypes();
    this.checkEditMode();
  }

  private initForm(): void {
    this.vehicleForm = this.fb.group({
      licensePlate: ['', [Validators.required]],
      vehicleTypeId: [null, [Validators.required]],
      seatCount: [null, [Validators.required, Validators.min(1)]],
      color: ['', [Validators.required]],
      basePrice: [null, [Validators.required, Validators.min(0)]],
      status: [VehicleStatus.AVAILABLE],
      imageUrl: ['']
    });
  }

  private loadVehicleTypes(): void {
    this.vehicleTypeService.getAllVehicleTypes().subscribe({
      next: (types) => {
        this.vehicleTypes = types;
      },
      error: (error) => {
        this.message.error('Failed to load vehicle types');
      }
    });
  }

  private checkEditMode(): void {
    const id = this.route.snapshot.paramMap.get('id');
    if (id) {
      this.isEditMode = true;
      this.vehicleId = +id;
      this.loadVehicle();
    }
  }

  private loadVehicle(): void {
    if (this.vehicleId) {
      this.loading = true;
      this.vehicleService.getVehicle(this.vehicleId).subscribe({
        next: (vehicle) => {
          this.vehicleForm.patchValue(vehicle);
          this.loading = false;
        },
        error: (error) => {
          this.message.error('Failed to load vehicle');
          this.loading = false;
        }
      });
    }
  }

  onSubmit(): void {
    if (this.vehicleForm.valid) {
      this.submitting = true;
      
      if (this.isEditMode && this.vehicleId) {
        const updateRequest: UpdateVehicleRequest = {
          id: this.vehicleId,
          ...this.vehicleForm.value
        };
        
        this.vehicleService.updateVehicle(updateRequest).subscribe({
          next: () => {
            this.message.success('Vehicle updated successfully');
            this.router.navigate(['/vehicles']);
          },
          error: (error) => {
            this.message.error('Failed to update vehicle');
            this.submitting = false;
          }
        });
      } else {
        const createRequest: CreateVehicleRequest = this.vehicleForm.value;
        
        this.vehicleService.createVehicle(createRequest).subscribe({
          next: () => {
            this.message.success('Vehicle created successfully');
            this.router.navigate(['/vehicles']);
          },
          error: (error) => {
            this.message.error('Failed to create vehicle');
            this.submitting = false;
          }
        });
      }
    } else {
      this.markFormGroupTouched();
    }
  }

  private markFormGroupTouched(): void {
    Object.keys(this.vehicleForm.controls).forEach(key => {
      const control = this.vehicleForm.get(key);
      if (control) {
        control.markAsTouched();
      }
    });
  }

  onCancel(): void {
    this.router.navigate(['/vehicles']);
  }
}
