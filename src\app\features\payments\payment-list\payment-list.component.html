<app-page-header 
  title="Payment Management" 
  subtitle="Track and manage payment records and receipts">
  
  <div nz-page-header-extra>
    <button nz-button nzType="primary" routerLink="/payments/create">
      <span nz-icon nzType="plus"></span>
      Add Payment
    </button>
  </div>
</app-page-header>

<nz-card>
  <form nz-form [formGroup]="filterForm" class="filter-form">
    <div nz-row [nzGutter]="[16, 16]">
      <div nz-col nzXs="24" nzSm="12" nzMd="6">
        <nz-form-item>
          <nz-form-label>Contract ID</nz-form-label>
          <nz-form-control>
            <nz-input-number formControlName="contractId" nzPlaceHolder="Enter contract ID" style="width: 100%"></nz-input-number>
          </nz-form-control>
        </nz-form-item>
      </div>
      
      <div nz-col nzXs="24" nzSm="12" nzMd="6">
        <nz-form-item>
          <nz-form-label>Payment Method</nz-form-label>
          <nz-form-control>
            <nz-select formControlName="paymentMethod" nzPlaceHolder="Select method" nzAllowClear>
              <nz-option *ngFor="let method of paymentMethodOptions" [nzValue]="method" [nzLabel]="method"></nz-option>
            </nz-select>
          </nz-form-control>
        </nz-form-item>
      </div>
      
      <div nz-col nzXs="24" nzSm="12" nzMd="6">
        <nz-form-item>
          <nz-form-label>Status</nz-form-label>
          <nz-form-control>
            <nz-select formControlName="status" nzPlaceHolder="Select status" nzAllowClear>
              <nz-option *ngFor="let status of paymentStatusOptions" [nzValue]="status" [nzLabel]="status"></nz-option>
            </nz-select>
          </nz-form-control>
        </nz-form-item>
      </div>
      
      <div nz-col nzXs="24" nzSm="12" nzMd="6">
        <nz-form-item>
          <nz-form-label>Payment Date From</nz-form-label>
          <nz-form-control>
            <nz-date-picker formControlName="startDate" nzPlaceHolder="Select date" style="width: 100%"></nz-date-picker>
          </nz-form-control>
        </nz-form-item>
      </div>
      
      <div nz-col nzXs="24" nzSm="12" nzMd="6">
        <nz-form-item>
          <nz-form-label>Payment Date To</nz-form-label>
          <nz-form-control>
            <nz-date-picker formControlName="endDate" nzPlaceHolder="Select date" style="width: 100%"></nz-date-picker>
          </nz-form-control>
        </nz-form-item>
      </div>
      
      <div nz-col nzXs="24" nzSm="12" nzMd="6">
        <nz-form-item>
          <nz-form-label>Min Amount</nz-form-label>
          <nz-form-control>
            <nz-input-number formControlName="minAmount" nzPlaceHolder="Min amount" [nzMin]="0" style="width: 100%"></nz-input-number>
          </nz-form-control>
        </nz-form-item>
      </div>
      
      <div nz-col nzXs="24" nzSm="12" nzMd="6">
        <nz-form-item>
          <nz-form-label>Max Amount</nz-form-label>
          <nz-form-control>
            <nz-input-number formControlName="maxAmount" nzPlaceHolder="Max amount" [nzMin]="0" style="width: 100%"></nz-input-number>
          </nz-form-control>
        </nz-form-item>
      </div>
      
      <div nz-col nzXs="24" nzSm="12" nzMd="6">
        <nz-form-item>
          <nz-form-label>&nbsp;</nz-form-label>
          <nz-form-control>
            <button nz-button nzType="default" (click)="resetFilters()">
              <span nz-icon nzType="reload"></span>
              Reset
            </button>
          </nz-form-control>
        </nz-form-item>
      </div>
    </div>
  </form>
</nz-card>

<nz-card style="margin-top: 16px;">
  <nz-table 
    #paymentTable
    [nzData]="payments"
    [nzLoading]="loading"
    [nzTotal]="total"
    [nzPageSize]="pageSize"
    [nzPageIndex]="pageIndex"
    [nzShowSizeChanger]="true"
    [nzShowQuickJumper]="true"
    [nzShowTotal]="totalTemplate"
    (nzPageIndexChange)="onPageChange($event)"
    (nzPageSizeChange)="onPageSizeChange($event)">
    
    <thead>
      <tr>
        <th>Payment Info</th>
        <th>Contract</th>
        <th>Customer</th>
        <th>Amount</th>
        <th>Payment Method</th>
        <th>Status</th>
        <th>Note</th>
        <th>Actions</th>
      </tr>
    </thead>
    
    <tbody>
      <tr *ngFor="let payment of payments">
        <td>
          <div><strong>#{{ payment.id }}</strong></div>
          <div style="color: #666; font-size: 12px;">{{ payment.paymentDate | date:'dd/MM/yyyy HH:mm' }}</div>
        </td>
        <td>
          <div><strong>{{ payment.contract?.contractNumber }}</strong></div>
          <div style="color: #666; font-size: 12px;">Contract #{{ payment.contractId }}</div>
        </td>
        <td>
          <div><strong>{{ payment.contract?.customer?.fullName }}</strong></div>
        </td>
        <td>
          <div><strong>{{ payment.amount | currencyFormat }}</strong></div>
        </td>
        <td>
          <div>
            <span nz-icon [nzType]="getPaymentMethodIcon(payment.paymentMethod)"></span>
            {{ payment.paymentMethod }}
          </div>
        </td>
        <td>
          <nz-tag [nzColor]="payment.status | statusColor">
            {{ payment.status }}
          </nz-tag>
        </td>
        <td>
          <div style="max-width: 150px; overflow: hidden; text-overflow: ellipsis;">
            {{ payment.note || '-' }}
          </div>
        </td>
        <td>
          <button nz-button nzType="link" nzSize="small" [routerLink]="['/payments/edit', payment.id]">
            <span nz-icon nzType="edit"></span>
            Edit
          </button>
          <nz-divider nzType="vertical"></nz-divider>
          <button nz-button nzType="link" nzSize="small" (click)="generateReceipt(payment)">
            <span nz-icon nzType="file-pdf"></span>
            Receipt
          </button>
          <nz-divider nzType="vertical"></nz-divider>
          <button nz-button nzType="link" nzSize="small" nzDanger (click)="showDeleteModal(payment)">
            <span nz-icon nzType="delete"></span>
            Delete
          </button>
        </td>
      </tr>
    </tbody>
  </nz-table>
  
  <ng-template #totalTemplate let-total let-range="range">
    {{ range[0] }}-{{ range[1] }} of {{ total }} payments
  </ng-template>
</nz-card>

<app-confirmation-modal
  [(visible)]="deleteModalVisible"
  title="Delete Payment"
  content="Are you sure you want to delete this payment record? This action cannot be undone."
  okText="Delete"
  okType="primary"
  [loading]="deleteLoading"
  (onOk)="confirmDelete()"
  (onCancel)="cancelDelete()">
</app-confirmation-modal>
