import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { RouterModule } from '@angular/router';
import { ReactiveFormsModule, FormsModule, FormBuilder, FormGroup } from '@angular/forms';
import { NzTableModule } from 'ng-zorro-antd/table';
import { NzButtonModule } from 'ng-zorro-antd/button';
import { NzInputModule } from 'ng-zorro-antd/input';
import { NzSelectModule } from 'ng-zorro-antd/select';
import { NzTagModule } from 'ng-zorro-antd/tag';
import { NzCardModule } from 'ng-zorro-antd/card';
import { NzFormModule } from 'ng-zorro-antd/form';
import { NzIconModule } from 'ng-zorro-antd/icon';
import { NzDividerModule } from 'ng-zorro-antd/divider';
import { NzDatePickerModule } from 'ng-zorro-antd/date-picker';
import { NzMessageService } from 'ng-zorro-antd/message';
import { Observable, BehaviorSubject } from 'rxjs';
import { debounceTime, distinctUntilChanged, switchMap } from 'rxjs/operators';

import { PageHeaderComponent } from '../../../shared/components/page-header/page-header.component';
import { ConfirmationModalComponent } from '../../../shared/components/confirmation-modal/confirmation-modal.component';
import { StatusColorPipe } from '../../../shared/pipes/status-color.pipe';
import { DriverService } from '../../../core/services';
import { Driver, DriverFilter, DriverStatus, PaginatedResponse } from '../../../core/models';

@Component({
  selector: 'app-driver-list',
  standalone: true,
  imports: [
    CommonModule,
    RouterModule,
    ReactiveFormsModule,
    FormsModule,
    NzTableModule,
    NzButtonModule,
    NzInputModule,
    NzSelectModule,
    NzTagModule,
    NzCardModule,
    NzFormModule,
    NzIconModule,
    NzDividerModule,
    NzDatePickerModule,
    PageHeaderComponent,
    ConfirmationModalComponent,
    StatusColorPipe
  ],
  templateUrl: './driver-list.component.html',
  styleUrls: ['./driver-list.component.scss']
})
export class DriverListComponent implements OnInit {
  drivers: Driver[] = [];
  loading = false;
  total = 0;
  pageSize = 10;
  pageIndex = 1;
  
  filterForm!: FormGroup;
  private filterSubject = new BehaviorSubject<DriverFilter>({});
  
  deleteModalVisible = false;
  deleteLoading = false;
  driverToDelete: Driver | null = null;
  
  driverStatusOptions = Object.values(DriverStatus);

  constructor(
    private fb: FormBuilder,
    private driverService: DriverService,
    private message: NzMessageService
  ) {}

  ngOnInit(): void {
    this.initFilterForm();
    this.setupFilterSubscription();
    this.loadDrivers();
  }

  private initFilterForm(): void {
    this.filterForm = this.fb.group({
      search: [''],
      status: [null],
      availableFrom: [null],
      availableTo: [null]
    });
  }

  private setupFilterSubscription(): void {
    this.filterForm.valueChanges.pipe(
      debounceTime(300),
      distinctUntilChanged()
    ).subscribe(values => {
      const filter: DriverFilter = {
        ...values,
        page: 1,
        limit: this.pageSize
      };
      this.filterSubject.next(filter);
      this.pageIndex = 1;
    });

    this.filterSubject.pipe(
      switchMap(filter => {
        this.loading = true;
        return this.driverService.getDrivers(filter);
      })
    ).subscribe({
      next: (response: PaginatedResponse<Driver>) => {
        this.drivers = response.data;
        this.total = response.total;
        this.loading = false;
      },
      error: (error) => {
        this.message.error('Failed to load drivers');
        this.loading = false;
      }
    });
  }

  private loadDrivers(): void {
    const filter: DriverFilter = {
      ...this.filterForm.value,
      page: this.pageIndex,
      limit: this.pageSize
    };
    this.filterSubject.next(filter);
  }

  onPageChange(pageIndex: number): void {
    this.pageIndex = pageIndex;
    this.loadDrivers();
  }

  onPageSizeChange(pageSize: number): void {
    this.pageSize = pageSize;
    this.pageIndex = 1;
    this.loadDrivers();
  }

  resetFilters(): void {
    this.filterForm.reset();
  }

  showDeleteModal(driver: Driver): void {
    this.driverToDelete = driver;
    this.deleteModalVisible = true;
  }

  confirmDelete(): void {
    if (this.driverToDelete) {
      this.deleteLoading = true;
      this.driverService.deleteDriver(this.driverToDelete.id).subscribe({
        next: () => {
          this.message.success('Driver deleted successfully');
          this.deleteModalVisible = false;
          this.deleteLoading = false;
          this.driverToDelete = null;
          this.loadDrivers();
        },
        error: (error) => {
          this.message.error('Failed to delete driver');
          this.deleteLoading = false;
        }
      });
    }
  }

  cancelDelete(): void {
    this.deleteModalVisible = false;
    this.driverToDelete = null;
  }

  isLicenseExpired(driver: Driver): boolean {
    return new Date(driver.licenseExpiryDate) < new Date();
  }

  updateDriverStatus(driver: Driver, status: DriverStatus): void {
    this.driverService.updateDriverStatus(driver.id, status).subscribe({
      next: () => {
        this.message.success('Driver status updated successfully');
        this.loadDrivers();
      },
      error: (error) => {
        this.message.error('Failed to update driver status');
      }
    });
  }
}
