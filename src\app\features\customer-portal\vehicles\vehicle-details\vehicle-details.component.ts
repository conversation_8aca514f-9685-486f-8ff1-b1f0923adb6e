import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { RouterModule, ActivatedRoute, Router } from '@angular/router';
import { ReactiveFormsModule, FormBuilder, FormGroup, Validators } from '@angular/forms';
import { NzCardModule } from 'ng-zorro-antd/card';
import { NzButtonModule } from 'ng-zorro-antd/button';
import { NzFormModule } from 'ng-zorro-antd/form';
import { NzIconModule } from 'ng-zorro-antd/icon';
import { NzTagModule } from 'ng-zorro-antd/tag';
import { NzDatePickerModule } from 'ng-zorro-antd/date-picker';
import { NzRadioModule } from 'ng-zorro-antd/radio';
import { NzDividerModule } from 'ng-zorro-antd/divider';

import { NzDescriptionsModule } from 'ng-zorro-antd/descriptions';
import { NzSpinModule } from 'ng-zorro-antd/spin';
import { NzMessageService } from 'ng-zorro-antd/message';

import { VehicleService } from '../../../../core/services';
import { Vehicle, RentalType } from '../../../../core/models';
import { CurrencyFormatPipe } from '../../../../shared/pipes/currency-format.pipe';
import { CustomerAuthService } from '../../../../core/services/customer-auth.service';

@Component({
  selector: 'app-vehicle-details',
  standalone: true,
  imports: [
    CommonModule,
    RouterModule,
    ReactiveFormsModule,
    NzCardModule,
    NzButtonModule,
    NzFormModule,
    NzIconModule,
    NzTagModule,
    NzDatePickerModule,
    NzRadioModule,
    NzDividerModule,

    NzDescriptionsModule,
    NzSpinModule,
    CurrencyFormatPipe
  ],
  templateUrl: './vehicle-details.component.html',
  styleUrls: ['./vehicle-details.component.scss']
})
export class VehicleDetailsComponent implements OnInit {
  vehicle: Vehicle | null = null;
  loading = false;
  bookingForm!: FormGroup;
  
  rentalTypeOptions = Object.values(RentalType);

  constructor(
    private route: ActivatedRoute,
    private router: Router,
    private fb: FormBuilder,
    private vehicleService: VehicleService,
    private customerAuthService: CustomerAuthService,
    private message: NzMessageService
  ) {}

  ngOnInit(): void {
    this.initBookingForm();
    this.loadVehicleDetails();
    this.loadQueryParams();
  }

  private initBookingForm(): void {
    const today = new Date();
    const tomorrow = new Date(today);
    tomorrow.setDate(tomorrow.getDate() + 1);

    this.bookingForm = this.fb.group({
      startDate: [today, [Validators.required]],
      endDate: [tomorrow, [Validators.required]],
      rentalType: [RentalType.SELF_DRIVE, [Validators.required]]
    });
  }

  private loadQueryParams(): void {
    this.route.queryParams.subscribe(params => {
      if (params['startDate']) {
        this.bookingForm.patchValue({
          startDate: new Date(params['startDate'])
        });
      }
      if (params['endDate']) {
        this.bookingForm.patchValue({
          endDate: new Date(params['endDate'])
        });
      }
    });
  }

  private loadVehicleDetails(): void {
    const vehicleId = this.route.snapshot.paramMap.get('id');
    if (vehicleId) {
      this.loading = true;
      this.vehicleService.getVehicle(+vehicleId).subscribe({
        next: (vehicle) => {
          this.vehicle = vehicle;
          this.loading = false;
        },
        error: (error) => {
          this.message.error('Failed to load vehicle details');
          this.loading = false;
          this.router.navigate(['/customer/vehicles']);
        }
      });
    }
  }

  calculateRentalDays(): number {
    const startDate = this.bookingForm.value.startDate;
    const endDate = this.bookingForm.value.endDate;
    
    if (startDate && endDate) {
      const timeDiff = endDate.getTime() - startDate.getTime();
      return Math.ceil(timeDiff / (1000 * 3600 * 24));
    }
    return 1;
  }

  calculateBasePrice(): number {
    if (!this.vehicle) return 0;
    return this.vehicle.basePrice * this.calculateRentalDays();
  }

  calculateDriverCost(): number {
    const rentalType = this.bookingForm.value.rentalType;
    if (rentalType === RentalType.WITH_DRIVER) {
      return 500000 * this.calculateRentalDays(); // 500k VND per day for driver
    }
    return 0;
  }

  calculateTotalPrice(): number {
    return this.calculateBasePrice() + this.calculateDriverCost();
  }

  onBookNow(): void {
    if (!this.customerAuthService.isAuthenticated()) {
      this.message.info('Please login to make a booking');
      this.router.navigate(['/customer/login'], {
        queryParams: { returnUrl: this.router.url }
      });
      return;
    }

    if (this.bookingForm.valid && this.vehicle) {
      const bookingData = {
        vehicleId: this.vehicle.id,
        startDate: this.bookingForm.value.startDate,
        endDate: this.bookingForm.value.endDate,
        rentalType: this.bookingForm.value.rentalType,
        totalPrice: this.calculateTotalPrice()
      };

      // Navigate to booking confirmation page
      this.router.navigate(['/customer/booking/confirm'], {
        state: { bookingData, vehicle: this.vehicle }
      });
    } else {
      this.markFormGroupTouched();
    }
  }

  private markFormGroupTouched(): void {
    Object.keys(this.bookingForm.controls).forEach(key => {
      const control = this.bookingForm.get(key);
      if (control) {
        control.markAsTouched();
      }
    });
  }

  goBack(): void {
    this.router.navigate(['/customer/vehicles']);
  }

  disabledDate = (current: Date): boolean => {
    const today = new Date();
    today.setHours(0, 0, 0, 0);
    return current && current < today;
  };

  disabledEndDate = (current: Date): boolean => {
    const startDate = this.bookingForm.value.startDate;
    if (!startDate) return false;
    
    const start = new Date(startDate);
    start.setHours(0, 0, 0, 0);
    return current && current <= start;
  };

  getVehicleFeatures(): string[] {
    // Mock features - in real app, this would come from the vehicle data
    return [
      'Air Conditioning',
      'GPS Navigation',
      'Bluetooth',
      'USB Charging',
      'Safety Features',
      'Comfortable Seating'
    ];
  }
}
