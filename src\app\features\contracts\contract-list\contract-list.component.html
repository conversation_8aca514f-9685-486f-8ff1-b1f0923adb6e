<app-page-header 
  title="Contract Management" 
  subtitle="Manage rental contracts for self-drive and with-driver rentals">
  
  <div nz-page-header-extra>
    <button nz-button nzType="primary" routerLink="/contracts/create">
      <span nz-icon nzType="plus"></span>
      New Contract
    </button>
  </div>
</app-page-header>

<nz-card>
  <form nz-form [formGroup]="filterForm" class="filter-form">
    <div nz-row [nz<PERSON>utter]="[16, 16]">
      <div nz-col nzXs="24" nzSm="12" nzMd="6">
        <nz-form-item>
          <nz-form-label>Search</nz-form-label>
          <nz-form-control>
            <input nz-input placeholder="Contract number, customer..." formControlName="search">
          </nz-form-control>
        </nz-form-item>
      </div>
      
      <div nz-col nzXs="24" nzSm="12" nzMd="6">
        <nz-form-item>
          <nz-form-label>Status</nz-form-label>
          <nz-form-control>
            <nz-select formControlName="status" nzPlaceHolder="Select status" nzAllowClear>
              <nz-option *ngFor="let status of contractStatusOptions" [nzValue]="status" [nzLabel]="status"></nz-option>
            </nz-select>
          </nz-form-control>
        </nz-form-item>
      </div>
      
      <div nz-col nzXs="24" nzSm="12" nzMd="6">
        <nz-form-item>
          <nz-form-label>Rental Type</nz-form-label>
          <nz-form-control>
            <nz-select formControlName="rentalType" nzPlaceHolder="Select type" nzAllowClear>
              <nz-option *ngFor="let type of rentalTypeOptions" [nzValue]="type" [nzLabel]="type"></nz-option>
            </nz-select>
          </nz-form-control>
        </nz-form-item>
      </div>
      
      <div nz-col nzXs="24" nzSm="12" nzMd="6">
        <nz-form-item>
          <nz-form-label>Start Date</nz-form-label>
          <nz-form-control>
            <nz-date-picker formControlName="startDate" nzPlaceHolder="Select date" style="width: 100%"></nz-date-picker>
          </nz-form-control>
        </nz-form-item>
      </div>
    </div>
    
    <div nz-row>
      <div nz-col nzSpan="24" class="filter-actions">
        <button nz-button nzType="default" (click)="resetFilters()">
          <span nz-icon nzType="reload"></span>
          Reset
        </button>
      </div>
    </div>
  </form>
</nz-card>

<nz-card style="margin-top: 16px;">
  <nz-table 
    #contractTable
    [nzData]="contracts"
    [nzLoading]="loading"
    [nzTotal]="total"
    [nzPageSize]="pageSize"
    [nzPageIndex]="pageIndex"
    [nzShowSizeChanger]="true"
    [nzShowQuickJumper]="true"
    [nzShowTotal]="totalTemplate"
    (nzPageIndexChange)="onPageChange($event)"
    (nzPageSizeChange)="onPageSizeChange($event)">
    
    <thead>
      <tr>
        <th>Contract Info</th>
        <th>Customer</th>
        <th>Vehicle</th>
        <th>Driver</th>
        <th>Rental Period</th>
        <th>Amount</th>
        <th>Status</th>
        <th>Actions</th>
      </tr>
    </thead>
    
    <tbody>
      <tr *ngFor="let contract of contracts">
        <td>
          <div><strong>{{ contract.contractNumber }}</strong></div>
          <div style="color: #666; font-size: 12px;">
            <nz-tag [nzColor]="contract.rentalType === 'SELF_DRIVE' ? 'blue' : 'green'">
              {{ contract.rentalType === 'SELF_DRIVE' ? 'Self Drive' : 'With Driver' }}
            </nz-tag>
          </div>
        </td>
        <td>
          <div><strong>{{ contract.customer?.fullName }}</strong></div>
          <div style="color: #666; font-size: 12px;">{{ contract.customer?.phone }}</div>
        </td>
        <td>
          <div><strong>{{ contract.vehicle?.licensePlate }}</strong></div>
          <div style="color: #666; font-size: 12px;">{{ contract.vehicle?.vehicleType?.nameVehicleType }}</div>
        </td>
        <td>
          <div *ngIf="contract.driver; else noDriver">
            <strong>{{ contract.driver.fullName }}</strong>
            <div style="color: #666; font-size: 12px;">{{ contract.driver.phone }}</div>
          </div>
          <ng-template #noDriver>
            <span style="color: #999;">Self Drive</span>
          </ng-template>
        </td>
        <td>
          <div>{{ contract.startDate | date:'dd/MM/yyyy' }} - {{ contract.endDate | date:'dd/MM/yyyy' }}</div>
          <div style="color: #666; font-size: 12px;">{{ getRentalDuration(contract) }} days</div>
        </td>
        <td>
          <div><strong>{{ contract.totalPrice | currencyFormat }}</strong></div>
          <div style="color: #666; font-size: 12px;">Deposit: {{ contract.depositAmount | currencyFormat }}</div>
        </td>
        <td>
          <nz-tag [nzColor]="contract.status | statusColor">
            {{ contract.status }}
          </nz-tag>
        </td>
        <td>
          <button nz-button nzType="link" nzSize="small" [routerLink]="['/contracts/edit', contract.id]">
            <span nz-icon nzType="edit"></span>
            Edit
          </button>
          <nz-divider nzType="vertical"></nz-divider>
          <button nz-button nzType="link" nzSize="small" (click)="generatePdf(contract)">
            <span nz-icon nzType="file-pdf"></span>
            PDF
          </button>
          <nz-divider nzType="vertical"></nz-divider>
          <button nz-button nzType="link" nzSize="small" nzDanger (click)="showDeleteModal(contract)">
            <span nz-icon nzType="delete"></span>
            Delete
          </button>
        </td>
      </tr>
    </tbody>
  </nz-table>
  
  <ng-template #totalTemplate let-total let-range="range">
    {{ range[0] }}-{{ range[1] }} of {{ total }} contracts
  </ng-template>
</nz-card>

<app-confirmation-modal
  [(visible)]="deleteModalVisible"
  title="Delete Contract"
  content="Are you sure you want to delete this contract? This action cannot be undone."
  okText="Delete"
  okType="primary"
  [loading]="deleteLoading"
  (onOk)="confirmDelete()"
  (onCancel)="cancelDelete()">
</app-confirmation-modal>
