<div class="login-container">
  <div class="login-card">
    <nz-card nzTitle="Car Rental Management System" class="login-form-card">
      <form nz-form [formGroup]="loginForm" (ngSubmit)="onSubmit()">
        <nz-form-item>
          <nz-form-label [nzSpan]="24" nzRequired>Username</nz-form-label>
          <nz-form-control [nzSpan]="24" nzErrorTip="Please enter your username">
            <input 
              nz-input 
              formControlName="username" 
              placeholder="Enter username"
              [nzSize]="'large'"
              prefix="user">
          </nz-form-control>
        </nz-form-item>

        <nz-form-item>
          <nz-form-label [nzSpan]="24" nzRequired>Password</nz-form-label>
          <nz-form-control [nzSpan]="24" nzErrorTip="Please enter your password">
            <nz-input-group [nzSuffix]="suffixTemplate">
              <input 
                [type]="passwordVisible ? 'text' : 'password'"
                nz-input 
                formControlName="password" 
                placeholder="Enter password"
                [nzSize]="'large'"
                prefix="lock">
            </nz-input-group>
            <ng-template #suffixTemplate>
              <span 
                nz-icon 
                [nzType]="passwordVisible ? 'eye-invisible' : 'eye'" 
                (click)="togglePasswordVisibility()"
                class="password-toggle">
              </span>
            </ng-template>
          </nz-form-control>
        </nz-form-item>

        <nz-form-item>
          <nz-form-control [nzSpan]="24">
            <label nz-checkbox formControlName="rememberMe">
              Remember me
            </label>
          </nz-form-control>
        </nz-form-item>

        <nz-form-item>
          <nz-form-control [nzSpan]="24">
            <button 
              nz-button 
              nzType="primary" 
              nzSize="large"
              [nzLoading]="isLoading"
              [disabled]="!loginForm.valid"
              class="login-button">
              Log In
            </button>
          </nz-form-control>
        </nz-form-item>
      </form>
    </nz-card>
  </div>
</div>
