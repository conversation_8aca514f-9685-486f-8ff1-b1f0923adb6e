export interface Vehicle {
  id: number;
  licensePlate: string;
  vehicleTypeId: number;
  seatCount: number;
  color: string;
  status: VehicleStatus;
  basePrice: number;
  imageUrl?: string;
  createdAt: Date;
  updatedAt: Date;
  vehicleType?: {
    id: number;
    nameVehicleType: string;
    description: string;
  };
}

export enum VehicleStatus {
  AVAILABLE = 'AVAILABLE',
  RENTED = 'RENTED',
  MAINTENANCE = 'MAINTENANCE',
  OUT_OF_SERVICE = 'OUT_OF_SERVICE'
}

export interface CreateVehicleRequest {
  licensePlate: string;
  vehicleTypeId: number;
  seatCount: number;
  color: string;
  basePrice: number;
  imageUrl?: string;
}

export interface UpdateVehicleRequest {
  id: number;
  licensePlate: string;
  vehicleTypeId: number;
  seatCount: number;
  color: string;
  status: VehicleStatus;
  basePrice: number;
  imageUrl?: string;
}

import { BaseFilter } from './auth.model';

export interface VehicleFilter extends BaseFilter {
  vehicleTypeId?: number;
  status?: VehicleStatus;
  seatCount?: number;
  minPrice?: number;
  maxPrice?: number;
  search?: string;
  startDate?: Date;
  endDate?: Date;
}
