.customer-layout {
  min-height: 100vh;
}

.header {
  background: #001529;
  padding: 0;
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 1000;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
}

.header-content {
  display: flex;
  align-items: center;
  justify-content: space-between;
  height: 64px;
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 20px;
}

.logo-section {
  display: flex;
  align-items: center;
  gap: 16px;
  
  .menu-toggle {
    display: none;
    color: white;
    
    &:hover {
      background: rgba(255, 255, 255, 0.1);
    }
  }
  
  .logo {
    display: flex;
    align-items: center;
    gap: 8px;
    color: white;
    text-decoration: none;
    cursor: pointer;
    
    &:hover {
      color: #1890ff;
    }
    
    .logo-icon {
      font-size: 24px;
    }
    
    .logo-text {
      font-size: 20px;
      font-weight: 600;
    }
  }
}

.nav-menu {
  flex: 1;
  display: flex;
  justify-content: center;
  
  .main-menu {
    background: transparent;
    border-bottom: none;
    
    ::ng-deep .ant-menu-item {
      color: rgba(255, 255, 255, 0.85);
      
      &:hover {
        color: #1890ff;
      }
      
      &.ant-menu-item-selected {
        color: #1890ff;
        background: rgba(24, 144, 255, 0.1);
      }
    }
  }
}

.user-section {
  .user-info {
    display: flex;
    align-items: center;
    gap: 8px;
    color: white;
    cursor: pointer;
    padding: 8px 12px;
    border-radius: 6px;
    transition: all 0.3s ease;
    
    &:hover {
      background: rgba(255, 255, 255, 0.1);
    }
    
    .user-name {
      font-weight: 500;
    }
    
    .dropdown-icon {
      font-size: 12px;
      opacity: 0.7;
    }
  }
}

.mobile-nav {
  display: none;
  position: fixed;
  top: 64px;
  left: 0;
  right: 0;
  background: white;
  border-bottom: 1px solid #f0f0f0;
  z-index: 999;
  transform: translateY(-100%);
  transition: transform 0.3s ease;
  
  &:not(.collapsed) {
    transform: translateY(0);
  }
  
  ::ng-deep .ant-menu {
    border-right: none;
    
    .ant-menu-item {
      margin: 0;
      
      &.ant-menu-item-selected {
        background: #e6f7ff;
        color: #1890ff;
      }
    }
  }
}

.content {
  margin-top: 64px;
  min-height: calc(100vh - 64px - 200px);
  background: #f5f5f5;
}

.footer {
  background: #001529;
  color: white;
  
  .footer-content {
    max-width: 1200px;
    margin: 0 auto;
    padding: 40px 20px 20px;
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 32px;
    
    .footer-section {
      h4 {
        color: white;
        font-size: 16px;
        font-weight: 600;
        margin-bottom: 16px;
      }
      
      p {
        color: rgba(255, 255, 255, 0.7);
        margin: 0;
      }
      
      ul {
        list-style: none;
        padding: 0;
        margin: 0;
        
        li {
          margin-bottom: 8px;
          
          a {
            color: rgba(255, 255, 255, 0.7);
            text-decoration: none;
            transition: color 0.3s ease;
            
            &:hover {
              color: #1890ff;
            }
          }
        }
      }
      
      .social-links {
        display: flex;
        gap: 12px;
        
        a {
          display: flex;
          align-items: center;
          justify-content: center;
          width: 36px;
          height: 36px;
          background: rgba(255, 255, 255, 0.1);
          border-radius: 50%;
          color: white;
          text-decoration: none;
          transition: all 0.3s ease;
          
          &:hover {
            background: #1890ff;
            transform: translateY(-2px);
          }
        }
      }
    }
  }
  
  .footer-bottom {
    border-top: 1px solid rgba(255, 255, 255, 0.1);
    padding: 20px;
    text-align: center;
    
    p {
      color: rgba(255, 255, 255, 0.7);
      margin: 0;
    }
  }
}

// Responsive design
@media (max-width: 768px) {
  .header-content {
    padding: 0 15px;
  }
  
  .logo-section .menu-toggle {
    display: block;
  }
  
  .nav-menu {
    display: none;
  }
  
  .mobile-nav {
    display: block;
  }
  
  .user-section .user-info .user-name {
    display: none;
  }
  
  .footer .footer-content {
    grid-template-columns: 1fr;
    gap: 24px;
    padding: 30px 15px 15px;
  }
}

@media (max-width: 576px) {
  .logo-section .logo .logo-text {
    display: none;
  }
  
  .user-section .user-info {
    padding: 8px;
    
    .dropdown-icon {
      display: none;
    }
  }
}
