import { Injectable } from '@angular/core';
import { CanActivate, CanActivateChild, Router } from '@angular/router';
import { Observable } from 'rxjs';
import { CustomerAuthService } from '../services/customer-auth.service';

@Injectable({
  providedIn: 'root'
})
export class CustomerAuthGuard implements CanActivate, CanActivateChild {
  constructor(
    private customerAuthService: CustomerAuthService,
    private router: Router
  ) {}

  canActivate(): Observable<boolean> | Promise<boolean> | boolean {
    return this.checkAuth();
  }

  canActivateChild(): Observable<boolean> | Promise<boolean> | boolean {
    return this.checkAuth();
  }

  private checkAuth(): boolean {
    if (this.customerAuthService.isAuthenticated()) {
      return true;
    }

    this.router.navigate(['/customer/login']);
    return false;
  }
}
