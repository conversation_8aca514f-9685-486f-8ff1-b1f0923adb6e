import { Injectable } from '@angular/core';
import { HttpClient, HttpParams } from '@angular/common/http';
import { Observable } from 'rxjs';
import { retry } from 'rxjs/operators';
import { ApiResponse, PaginatedResponse, BaseFilter } from '../models';

@Injectable({
  providedIn: 'root'
})
export abstract class BaseHttpService {
  protected abstract baseUrl: string;

  constructor(protected httpClient: HttpClient) {}

  protected get<T>(endpoint: string, params?: any): Observable<ApiResponse<T>> {
    const httpParams = this.buildHttpParams(params);
    return this.httpClient
      .get<ApiResponse<T>>(`${this.baseUrl}${endpoint}`, { params: httpParams })
      .pipe(retry(1));
  }

  protected getPaginated<T>(endpoint: string, filter?: BaseFilter & any): Observable<ApiResponse<PaginatedResponse<T>>> {
    const httpParams = this.buildHttpParams(filter);
    return this.httpClient
      .get<ApiResponse<PaginatedResponse<T>>>(`${this.baseUrl}${endpoint}`, { params: httpParams })
      .pipe(retry(1));
  }

  protected post<T>(endpoint: string, body: any): Observable<ApiResponse<T>> {
    return this.httpClient
      .post<ApiResponse<T>>(`${this.baseUrl}${endpoint}`, body)
      .pipe(retry(1));
  }

  protected put<T>(endpoint: string, body: any): Observable<ApiResponse<T>> {
    return this.httpClient
      .put<ApiResponse<T>>(`${this.baseUrl}${endpoint}`, body)
      .pipe(retry(1));
  }

  protected delete<T>(endpoint: string): Observable<ApiResponse<T>> {
    return this.httpClient
      .delete<ApiResponse<T>>(`${this.baseUrl}${endpoint}`)
      .pipe(retry(1));
  }

  protected downloadFile(endpoint: string, params?: any): Observable<Blob> {
    const httpParams = this.buildHttpParams(params);
    return this.httpClient
      .get(`${this.baseUrl}${endpoint}`, { 
        params: httpParams,
        responseType: 'blob' 
      })
      .pipe(retry(1));
  }

  private buildHttpParams(params?: any): HttpParams {
    let httpParams = new HttpParams();
    
    if (params) {
      Object.keys(params).forEach(key => {
        const value = params[key];
        if (value !== null && value !== undefined && value !== '') {
          if (value instanceof Date) {
            httpParams = httpParams.set(key, value.toISOString());
          } else if (Array.isArray(value)) {
            value.forEach(item => {
              httpParams = httpParams.append(key, item.toString());
            });
          } else {
            httpParams = httpParams.set(key, value.toString());
          }
        }
      });
    }
    
    return httpParams;
  }
}
