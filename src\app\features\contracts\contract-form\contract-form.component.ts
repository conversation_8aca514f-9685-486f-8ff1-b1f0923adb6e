import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { RouterModule, ActivatedRoute, Router } from '@angular/router';
import { ReactiveFormsModule, FormBuilder, FormGroup, Validators } from '@angular/forms';
import { NzFormModule } from 'ng-zorro-antd/form';
import { NzInputModule } from 'ng-zorro-antd/input';
import { NzInputNumberModule } from 'ng-zorro-antd/input-number';
import { NzButtonModule } from 'ng-zorro-antd/button';
import { NzCardModule } from 'ng-zorro-antd/card';
import { NzSelectModule } from 'ng-zorro-antd/select';
import { NzDatePickerModule } from 'ng-zorro-antd/date-picker';
import { NzRadioModule } from 'ng-zorro-antd/radio';
import { NzMessageService } from 'ng-zorro-antd/message';
import { PageHeaderComponent } from '../../../shared/components/page-header/page-header.component';
import { ContractService, CustomerService, VehicleService, DriverService } from '../../../core/services';
import { 
  Contract, 
  CreateContractRequest, 
  UpdateContractRequest, 
  ContractStatus, 
  RentalType,
  Customer,
  Vehicle,
  Driver
} from '../../../core/models';
import { CustomValidators } from '../../../shared/validators/custom.validators';

@Component({
  selector: 'app-contract-form',
  standalone: true,
  imports: [
    CommonModule,
    RouterModule,
    ReactiveFormsModule,
    NzFormModule,
    NzInputModule,
    NzInputNumberModule,
    NzButtonModule,
    NzCardModule,
    NzSelectModule,
    NzDatePickerModule,
    NzRadioModule,
    PageHeaderComponent
  ],
  templateUrl: './contract-form.component.html',
  styleUrls: ['./contract-form.component.scss']
})
export class ContractFormComponent implements OnInit {
  contractForm!: FormGroup;
  isEditMode = false;
  contractId: number | null = null;
  loading = false;
  submitting = false;

  customers: Customer[] = [];
  vehicles: Vehicle[] = [];
  drivers: Driver[] = [];
  availableVehicles: Vehicle[] = [];
  availableDrivers: Driver[] = [];

  contractStatusOptions = Object.values(ContractStatus);
  rentalTypeOptions = Object.values(RentalType);

  constructor(
    private fb: FormBuilder,
    private route: ActivatedRoute,
    private router: Router,
    private contractService: ContractService,
    private customerService: CustomerService,
    private vehicleService: VehicleService,
    private driverService: DriverService,
    private message: NzMessageService
  ) {}

  ngOnInit(): void {
    this.initForm();
    this.loadInitialData();
    this.checkEditMode();
    this.setupFormSubscriptions();
  }

  private initForm(): void {
    this.contractForm = this.fb.group({
      contractNumber: ['', [Validators.required, CustomValidators.contractNumber]],
      customerId: [null, [Validators.required]],
      vehicleId: [null, [Validators.required]],
      driverId: [null],
      rentalType: [RentalType.SELF_DRIVE, [Validators.required]],
      startDate: [null, [Validators.required, CustomValidators.futureDate]],
      endDate: [null, [Validators.required, CustomValidators.futureDate]],
      totalPrice: [null, [Validators.required, CustomValidators.positiveNumber]],
      depositAmount: [null, [Validators.required, CustomValidators.positiveNumber]],
      status: [ContractStatus.PENDING]
    });
  }

  private setupFormSubscriptions(): void {
    // Watch rental type changes
    this.contractForm.get('rentalType')?.valueChanges.subscribe(rentalType => {
      const driverControl = this.contractForm.get('driverId');
      if (rentalType === RentalType.WITH_DRIVER) {
        driverControl?.setValidators([Validators.required]);
      } else {
        driverControl?.clearValidators();
        driverControl?.setValue(null);
      }
      driverControl?.updateValueAndValidity();
    });

    // Watch date changes to filter available vehicles and drivers
    this.contractForm.get('startDate')?.valueChanges.subscribe(() => {
      this.updateAvailableResources();
    });

    this.contractForm.get('endDate')?.valueChanges.subscribe(() => {
      this.updateAvailableResources();
    });

    // Add date range validation
    this.contractForm.setValidators(CustomValidators.dateRange('startDate', 'endDate'));
  }

  private loadInitialData(): void {
    // Load customers
    this.customerService.getCustomers({ limit: 1000 }).subscribe({
      next: (response) => {
        this.customers = response.data;
      },
      error: () => {
        this.message.error('Failed to load customers');
      }
    });

    // Load all vehicles and drivers initially
    this.vehicleService.getVehicles({ limit: 1000 }).subscribe({
      next: (response) => {
        this.vehicles = response.data;
        this.availableVehicles = [...this.vehicles];
      },
      error: () => {
        this.message.error('Failed to load vehicles');
      }
    });

    this.driverService.getDrivers({ limit: 1000 }).subscribe({
      next: (response) => {
        this.drivers = response.data;
        this.availableDrivers = [...this.drivers];
      },
      error: () => {
        this.message.error('Failed to load drivers');
      }
    });
  }

  private updateAvailableResources(): void {
    const startDate = this.contractForm.get('startDate')?.value;
    const endDate = this.contractForm.get('endDate')?.value;

    if (startDate && endDate) {
      // Load available vehicles for the selected date range
      this.vehicleService.getAvailableVehicles(startDate, endDate).subscribe({
        next: (vehicles) => {
          this.availableVehicles = vehicles;
          // Reset vehicle selection if current selection is not available
          const currentVehicleId = this.contractForm.get('vehicleId')?.value;
          if (currentVehicleId && !vehicles.find(v => v.id === currentVehicleId)) {
            this.contractForm.get('vehicleId')?.setValue(null);
          }
        },
        error: () => {
          this.message.error('Failed to load available vehicles');
        }
      });

      // Load available drivers for the selected date range
      this.driverService.getAvailableDrivers(startDate, endDate).subscribe({
        next: (drivers) => {
          this.availableDrivers = drivers;
          // Reset driver selection if current selection is not available
          const currentDriverId = this.contractForm.get('driverId')?.value;
          if (currentDriverId && !drivers.find(d => d.id === currentDriverId)) {
            this.contractForm.get('driverId')?.setValue(null);
          }
        },
        error: () => {
          this.message.error('Failed to load available drivers');
        }
      });
    }
  }

  private checkEditMode(): void {
    const id = this.route.snapshot.paramMap.get('id');
    if (id) {
      this.isEditMode = true;
      this.contractId = +id;
      this.loadContract();
    } else {
      // Generate contract number for new contracts
      this.generateContractNumber();
    }
  }

  private generateContractNumber(): void {
    const now = new Date();
    const year = now.getFullYear();
    const month = (now.getMonth() + 1).toString().padStart(2, '0');
    const day = now.getDate().toString().padStart(2, '0');
    const random = Math.floor(Math.random() * 1000).toString().padStart(3, '0');
    const contractNumber = `CR${year}${month}${day}${random}`;
    this.contractForm.get('contractNumber')?.setValue(contractNumber);
  }

  private loadContract(): void {
    if (this.contractId) {
      this.loading = true;
      this.contractService.getContract(this.contractId).subscribe({
        next: (contract) => {
          this.contractForm.patchValue({
            ...contract,
            startDate: new Date(contract.startDate),
            endDate: new Date(contract.endDate)
          });
          this.loading = false;
        },
        error: (error) => {
          this.message.error('Failed to load contract');
          this.loading = false;
        }
      });
    }
  }

  onSubmit(): void {
    if (this.contractForm.valid) {
      this.submitting = true;
      
      const formValue = this.contractForm.value;
      const contractData = {
        ...formValue,
        startDate: new Date(formValue.startDate),
        endDate: new Date(formValue.endDate)
      };
      
      if (this.isEditMode && this.contractId) {
        const updateRequest: UpdateContractRequest = {
          id: this.contractId,
          ...contractData
        };
        
        this.contractService.updateContract(updateRequest).subscribe({
          next: () => {
            this.message.success('Contract updated successfully');
            this.router.navigate(['/contracts']);
          },
          error: (error) => {
            this.message.error('Failed to update contract');
            this.submitting = false;
          }
        });
      } else {
        const createRequest: CreateContractRequest = contractData;
        
        this.contractService.createContract(createRequest).subscribe({
          next: () => {
            this.message.success('Contract created successfully');
            this.router.navigate(['/contracts']);
          },
          error: (error) => {
            this.message.error('Failed to create contract');
            this.submitting = false;
          }
        });
      }
    } else {
      this.markFormGroupTouched();
    }
  }

  private markFormGroupTouched(): void {
    Object.keys(this.contractForm.controls).forEach(key => {
      const control = this.contractForm.get(key);
      if (control) {
        control.markAsTouched();
      }
    });
  }

  onCancel(): void {
    this.router.navigate(['/contracts']);
  }

  disabledDate = (current: Date): boolean => {
    const today = new Date();
    today.setHours(0, 0, 0, 0);
    return current && current < today;
  };

  calculateTotalPrice(): void {
    const vehicleId = this.contractForm.get('vehicleId')?.value;
    const startDate = this.contractForm.get('startDate')?.value;
    const endDate = this.contractForm.get('endDate')?.value;
    const rentalType = this.contractForm.get('rentalType')?.value;

    if (vehicleId && startDate && endDate) {
      const vehicle = this.availableVehicles.find(v => v.id === vehicleId);
      if (vehicle) {
        const days = Math.ceil((endDate.getTime() - startDate.getTime()) / (1000 * 3600 * 24));
        let totalPrice = vehicle.basePrice * days;
        
        // Add driver cost if with driver
        if (rentalType === RentalType.WITH_DRIVER) {
          totalPrice += 500000 * days; // Driver cost per day
        }

        this.contractForm.get('totalPrice')?.setValue(totalPrice);
        this.contractForm.get('depositAmount')?.setValue(Math.round(totalPrice * 0.3)); // 30% deposit
      }
    }
  }
}
