import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { RouterModule, Router } from '@angular/router';
import { ReactiveFormsModule, FormBuilder, FormGroup, Validators } from '@angular/forms';
import { NzCardModule } from 'ng-zorro-antd/card';
import { NzButtonModule } from 'ng-zorro-antd/button';
import { NzFormModule } from 'ng-zorro-antd/form';
import { NzInputModule } from 'ng-zorro-antd/input';
import { NzSelectModule } from 'ng-zorro-antd/select';
import { NzDividerModule } from 'ng-zorro-antd/divider';
import { NzDescriptionsModule } from 'ng-zorro-antd/descriptions';
import { NzStepsModule } from 'ng-zorro-antd/steps';
import { NzMessageService } from 'ng-zorro-antd/message';

import { ContractService } from '../../../../core/services';
import { CustomerAuthService } from '../../../../core/services/customer-auth.service';
import { Vehicle, CreateContractRequest, RentalType, PaymentMethod } from '../../../../core/models';
import { CurrencyFormatPipe } from '../../../../shared/pipes/currency-format.pipe';

interface BookingData {
  vehicleId: number;
  startDate: Date;
  endDate: Date;
  rentalType: RentalType;
  totalPrice: number;
}

@Component({
  selector: 'app-booking-confirm',
  standalone: true,
  imports: [
    CommonModule,
    RouterModule,
    ReactiveFormsModule,
    NzCardModule,
    NzButtonModule,
    NzFormModule,
    NzInputModule,
    NzSelectModule,
    NzDividerModule,
    NzDescriptionsModule,
    NzStepsModule,
    CurrencyFormatPipe
  ],
  templateUrl: './booking-confirm.component.html',
  styleUrls: ['./booking-confirm.component.scss']
})
export class BookingConfirmComponent implements OnInit {
  bookingData: BookingData | null = null;
  vehicle: Vehicle | null = null;
  confirmationForm!: FormGroup;
  submitting = false;
  currentStep = 0;

  paymentMethodOptions = Object.values(PaymentMethod);

  constructor(
    private fb: FormBuilder,
    private router: Router,
    private contractService: ContractService,
    private customerAuthService: CustomerAuthService,
    private message: NzMessageService
  ) {
    // Get data from navigation state
    const navigation = this.router.getCurrentNavigation();
    if (navigation?.extras.state) {
      this.bookingData = navigation.extras.state['bookingData'];
      this.vehicle = navigation.extras.state['vehicle'];
    }
  }

  ngOnInit(): void {
    // Redirect if no booking data
    if (!this.bookingData || !this.vehicle) {
      this.message.error('Invalid booking data');
      this.router.navigate(['/customer/vehicles']);
      return;
    }

    // Redirect if not authenticated
    if (!this.customerAuthService.isAuthenticated()) {
      this.message.info('Please login to complete your booking');
      this.router.navigate(['/customer/login']);
      return;
    }

    this.initConfirmationForm();
  }

  private initConfirmationForm(): void {
    this.confirmationForm = this.fb.group({
      paymentMethod: [PaymentMethod.CASH, [Validators.required]],
      specialRequests: [''],
      agreeToTerms: [false, [Validators.requiredTrue]]
    });
  }

  calculateRentalDays(): number {
    if (!this.bookingData) return 0;
    const timeDiff = this.bookingData.endDate.getTime() - this.bookingData.startDate.getTime();
    return Math.ceil(timeDiff / (1000 * 3600 * 24));
  }

  calculateBasePrice(): number {
    if (!this.vehicle || !this.bookingData) return 0;
    return this.vehicle.basePrice * this.calculateRentalDays();
  }

  calculateDriverCost(): number {
    if (!this.bookingData) return 0;
    if (this.bookingData.rentalType === RentalType.WITH_DRIVER) {
      return 500000 * this.calculateRentalDays();
    }
    return 0;
  }

  calculateDepositAmount(): number {
    if (!this.bookingData) return 0;
    return Math.round(this.bookingData.totalPrice * 0.3); // 30% deposit
  }

  onConfirmBooking(): void {
    if (!this.confirmationForm.valid || !this.bookingData || !this.vehicle) {
      this.markFormGroupTouched();
      return;
    }

    this.submitting = true;
    const currentCustomer = this.customerAuthService.getCurrentCustomer();
    
    if (!currentCustomer) {
      this.message.error('User session expired. Please login again.');
      this.router.navigate(['/customer/login']);
      return;
    }

    const contractRequest: CreateContractRequest = {
      contractNumber: this.generateContractNumber(),
      customerId: currentCustomer.id,
      vehicleId: this.bookingData.vehicleId,
      driverId: this.bookingData.rentalType === RentalType.WITH_DRIVER ? undefined : undefined, // Will be assigned by admin
      rentalType: this.bookingData.rentalType,
      startDate: this.bookingData.startDate,
      endDate: this.bookingData.endDate,
      totalPrice: this.bookingData.totalPrice,
      depositAmount: this.calculateDepositAmount()
    };

    this.contractService.createContract(contractRequest).subscribe({
      next: (contract) => {
        this.message.success('Booking confirmed successfully!');
        this.router.navigate(['/customer/bookings'], {
          queryParams: { newBooking: contract.id }
        });
      },
      error: (error) => {
        this.message.error(error.error?.message || 'Failed to confirm booking. Please try again.');
        this.submitting = false;
      }
    });
  }

  private generateContractNumber(): string {
    const now = new Date();
    const year = now.getFullYear();
    const month = (now.getMonth() + 1).toString().padStart(2, '0');
    const day = now.getDate().toString().padStart(2, '0');
    const random = Math.floor(Math.random() * 1000).toString().padStart(3, '0');
    return `CR${year}${month}${day}${random}`;
  }

  private markFormGroupTouched(): void {
    Object.keys(this.confirmationForm.controls).forEach(key => {
      const control = this.confirmationForm.get(key);
      if (control) {
        control.markAsTouched();
      }
    });
  }

  goBack(): void {
    if (this.vehicle) {
      this.router.navigate(['/customer/vehicles', this.vehicle.id]);
    } else {
      this.router.navigate(['/customer/vehicles']);
    }
  }

  nextStep(): void {
    if (this.currentStep < 2) {
      this.currentStep++;
    }
  }

  prevStep(): void {
    if (this.currentStep > 0) {
      this.currentStep--;
    }
  }
}
