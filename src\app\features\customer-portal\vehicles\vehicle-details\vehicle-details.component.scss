.vehicle-details-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 20px;
  background: #f5f5f5;
  min-height: 100vh;
}

.page-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 24px;
  background: white;
  padding: 20px;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  
  .back-button {
    color: #1890ff;
    padding: 0;
    
    &:hover {
      color: #40a9ff;
    }
  }
  
  h1 {
    margin: 0;
    font-size: 28px;
    font-weight: 600;
    color: #333;
  }
}

.image-card {
  margin-bottom: 24px;
  border-radius: 12px;
  overflow: hidden;
  
  ::ng-deep .ant-card-body {
    padding: 0;
  }
}

.vehicle-image-container {
  .main-image {
    width: 100%;
    height: 400px;
    object-fit: cover;
    
    ::ng-deep img {
      width: 100%;
      height: 100%;
      object-fit: cover;
    }
  }
}

.specs-card,
.features-card,
.description-card {
  margin-bottom: 24px;
  border-radius: 12px;
  
  ::ng-deep .ant-card-head {
    border-bottom: 1px solid #f0f0f0;
    
    .ant-card-head-title {
      font-size: 18px;
      font-weight: 600;
    }
  }
}

.features-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 16px;
  
  .feature-item {
    display: flex;
    align-items: center;
    padding: 12px;
    background: #f8f9fa;
    border-radius: 8px;
    
    .feature-icon {
      color: #52c41a;
      margin-right: 8px;
      font-size: 16px;
    }
  }
}

.booking-panel {
  position: sticky;
  top: 20px;
}

.booking-card {
  border-radius: 12px;
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
  
  ::ng-deep .ant-card-head {
    border-bottom: 1px solid #f0f0f0;
    
    .ant-card-head-title {
      font-size: 20px;
      font-weight: 600;
      color: #1890ff;
    }
  }
}

.rental-option {
  margin-bottom: 16px;
  
  label {
    width: 100%;
    padding: 16px;
    border: 2px solid #f0f0f0;
    border-radius: 8px;
    cursor: pointer;
    transition: all 0.3s ease;
    
    &:hover {
      border-color: #1890ff;
    }
    
    ::ng-deep .ant-radio-checked + .ant-radio-inner {
      border-color: #1890ff;
      background-color: #1890ff;
    }
  }
  
  .option-content {
    margin-left: 8px;
    
    .option-title {
      font-weight: 600;
      color: #333;
    }
    
    .option-description {
      font-size: 12px;
      color: #666;
      margin-top: 4px;
    }
  }
}

.pricing-summary {
  background: #f8f9fa;
  padding: 20px;
  border-radius: 8px;
  margin-bottom: 24px;
  
  .pricing-row {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 12px;
    
    &:last-child {
      margin-bottom: 0;
    }
    
    &.total {
      font-size: 18px;
      font-weight: 600;
      color: #1890ff;
    }
    
    .value {
      font-weight: 500;
    }
  }
}

.book-button {
  width: 100%;
  height: 48px;
  font-size: 16px;
  font-weight: 600;
  margin-bottom: 24px;
}

.additional-info {
  .info-item {
    display: flex;
    align-items: center;
    margin-bottom: 12px;
    color: #666;
    
    span:first-child {
      color: #52c41a;
      margin-right: 8px;
      font-size: 16px;
    }
    
    &:last-child {
      margin-bottom: 0;
    }
  }
}

.loading-container {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 400px;
}

// Responsive design
@media (max-width: 768px) {
  .vehicle-details-container {
    padding: 10px;
  }
  
  .page-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 12px;
    
    h1 {
      font-size: 24px;
    }
  }
  
  .vehicle-image-container .main-image {
    height: 250px;
  }
  
  .features-grid {
    grid-template-columns: 1fr;
  }
  
  .booking-panel {
    position: static;
    margin-top: 24px;
  }
  
  .rental-option label {
    padding: 12px;
  }
}

@media (max-width: 576px) {
  .page-header {
    padding: 15px;
    
    h1 {
      font-size: 20px;
    }
  }
  
  .vehicle-image-container .main-image {
    height: 200px;
  }
  
  .pricing-summary {
    padding: 15px;
  }
}
